---
Project:
  - 项目：仓储工作经验总结
---
## 内容大纲

- 任务系统的基本功能
	- 以少驭多
		- 可以快速收集各种要做的事
		- 可以建立一套整理系统，把事情慢慢过滤、分类成项目
		- 可以从项目的视野，去管理所有杂事
	- ⾏动之间建立关联（设计具有成果的任务）
		- ⼤多数⾏动都是有所关联的，只是我们都是断裂的处理他们，导致⾏动的价值、效率都降低（让⼀个⾏动，除了本⾝完成，还可以为下⼀个⾏动或其他⾏动累积价值，共同衍生出更有价值的目标、成果）
	- 任务拆解
		- 把任务里⾯的⼀步步具体⾏动拆解出来，我才能知道什么事情真正应该列在待办清单，并且在看到待办清单时，真正明确的知道⾃⼰要做什么
		- 拆解困难任务变成更具体的下一步行动
	- 项目整合（价值判断、情景选择）
		- 每一个项目的任务跟行动，甚至包含数据，都摆放在同一个位置，才能够一次看到所有我需要执行的步骤
		- 依据个性化需求进行任务分类、整合
		- 权重判断
			- 系统要能帮我们选择优先权重
			- 依据两个⾏动背后关联着的那个项目⽬目标价值孰轻孰重来选择与判断
		- 情景选择
			- 选择正确情境做最有价值的事（在当下这个空档情境， 最适合采取哪个⾏动？）
	- 主动提⽰
		- ⽤时间提醒来主动提⽰（⼀个项目里， 哪个任务、哪个⾏动，相对来说应该在哪个时间点开始启动？）
		- ⽤统⼀的排程系统来主动提⽰（把⾏动、任务，放在我需要时⼀定会经过的地⽅）
- 具体实践细节
	- 创建任务
		- 锁定有价值的事情， 把那些有价值、可以带来成果的事情，变成⼀个⼀个任务，然后从任务的⾓度，开始思考
		- 原则：⼀个任务、⼀则笔记（⽤任务成果来对焦，会让整理更简单且不会遗漏）
		- 编辑笔记技巧
			- 主题树状⼤纲（要解决的3个问题）
				- 让笔记顺序不杂乱、分散，任务拆解有次序
				- 让思路有逻辑，让任务思考有层次（思考：主要重点是什么？逻辑顺序是什么？）
				- 让重点凸显，明确关键的⾏动（适合同时展开多条线索，每次找到哪一条线索的新重点，就纳入该条线索中，多条线索可以同时整理）
			- 快速写下⾏动、交办
				- 4个关键词
					- 行动：具体要做的是什么行动？
					- 对谁：有没有明确要对谁行动？（如果没有，可省略）
					- 成果：具体要完成的是什么成果？
					- 时地：有没有明确要执行或完成的时间地点？（如果没有，可省略）
				- 注意可以使用［空格］隔开关键字词，已达到快速记录的目的
	- 拆解、整理任务
		- 结构
			- 想要获得什么具体成果或解决什么具体问题？
			- 拆解出需要做的下一步行动
			- 整合任务需要的相关数据
		- 思考「这件事情到底我想要达成什么具体成果？」并且把这个具体的愿景描述出来。
			- 设想的具体愿景不同，而会衍生出不同的行动方式
		- 拆解出需要做的下⼀步⾏动
			- 固执于每天⾏动清单，反⽽真正实现的机会降低了。回到每⼀个具体的任务笔记当中，去拆解出这个任务可能可以实现的「可能性的⾏动清单」
			- 具体操作技巧
				- 聚焦在一个真正值得完成的任务成果上，并且把任务的所有环节，统一在一则笔记当中进行管理
				- 当任务可以拆解出具体下一步行动清单，这时候这个任务就可以被更有效地往下推进
				- 4个思考方向（当你发现任务⾛不通、卡住、拖延， 那么可以从这四种⽅向里⾯， 去寻找⼀个⽅向）
					- 上， 我需要先做什么？ 这个任务需要哪些前置⾏动？有哪些前置条件？
						- 往上思考，就是往前回推，如果要做到这个行动，有没有比他更前一步的行动必须先做呢？有没有比前一步行动再更前一步的行动必须先做呢？
					- 下，我能继续做什么？完成这个⾏动后，我还可以继续采取哪些⾏动？
						- 往下思考，就是往后延伸，如果这个行动完成了，我还能继续采取什么行动，让这个成果变得更好吗？或者有没有其他任务会跟这个行动相关，可以把彼此连接在一起呢？
					- 左，我还可以做什么？这条路如果⾛不通，可以换成做什么？
						- 往左思考，就是水平思考，去开展这个行动有没有其他可以替换的行动？有没有其他可能性？如果原本预设的这个行动走不通，有没有其他可能可以采取的作法？也可以达到类似的成果呢？
					- 右，我还想要做什么？这个任务我有没有其他想要的成果？
						- 透过开放式的水平思考，我们不需要固执在唯一的行动上而裹足不前
						- 如果原本的这个行动走不通的时候，有没有可能替换其他也想要达到的成果（类似的成果）
	- 笔记归类（可快速定位需要的内容、方便管理和再利用）
		- 标签分类法
			- 为每一则笔记（核心任务笔记、数据）设定一个标签
			- 利用搜索功能可以快速找到某个主题的所有内容
		- 目录清单分类法（根据需求设定一个主题，可以达到标签无法实现的效果）
			- 方法1：
				- 为当前目标、需求设定一个项目
				- 将项目相关的所有【核心任务笔记】、【数据】链接到目录
			- 方法2：
				- 为当前目标、需求设定一个项目
				- 将项目相关的所有【核心任务笔记】、【数据】、【核心任务笔记】的部分行动或其下层内容链接至目录，调整它们之间的逻辑顺序，使用简单的描述性文本将其全部串连起来，可以让这些笔记更加直观、更加有条理
		- 原则
			- 信息、数据、想法放在行动下会更加直观
			- 要先把位置设定好，让项目都有⼀个项目主⽬录，每个项目主⽬录上就是所有的任务、⾏动应该摆放的位置。当这个架构设定好了，任何新的任务和⾏动出现的时候，我们只做⼀件事情，就是把它摆进项目主⽬录的正确位置上
	- 提醒：不是当下要做什么的提醒，⽽是什么时间点应该开始做准备的提醒。（决定这个任务笔记「应该什么时候开始准备」，然后把那个准备时间变成提醒时间）
	- ⽬标选择与时间情境（去做这个时间最适合做的事，并且逐步的走在自己的目标上面）
		- 思路
			- 掌控好自己生活、工作的基本流程与情境；
			- 把目标放入项目流程与情境中；
			- 为必定出现的流程与情境，规划好利用方式；
		- 判断目标重要性（把项目任务纳入这样的进度流程来管理；举例「购物欲望清单」）
			- 第一个进度叫做：想要，表示这个购物任务还在思考产品的优缺点，还在决定我的需求点，还在判断的阶段
			- 第二个进度叫做：需要，已经进入了确认要购买的阶段，只是要等待一个适合购买的时机点。例如我想奖励自己的时候
			- 第三个进度叫做：满足，表示这个任务已经进入了完成阶段，而我需要偶尔检验一下
		- 情景（什么时候应该推进什么任务或选择什么任务执行）
			- 工作（举例）
				- 【重要】：那些最近几个⽉截⽌⽇就要到的项目，或是我判断为⽬前这个阶段对我来说最重要的⼈⽣项目，我就把他的项目笔记，加上重要这个标签
				- 【计划】：另外还有个进度是计划，有很多项目八字还没⼀撇，是构思阶段。有些项目，还正在准备中，还没排出真正的项目进度时程。我就加上计划的标签。
				- 【追踪】：有些事情已经完成了，但是偶尔也要追踪⼀下他后续延伸的效益、成果，这是我⼯作上需要管理的⼀种流程，我就把这些任务笔记和项目笔记，加上追踪的卷标。
			- 写作（举例）
				- 【测试】：其中有些写作任务，包含⼀个我很想测试看看的⼯具，或者很想测试看看的⽅法，但是要测试就必须要有可以测试的时间与情境。
				- 【思考】：有些写作任务，是我正在构思的某个⽅法论难题，或是正在构思的某种⼯作问题解法，这些任务需要的是在适合思考的情境，去想出解决这些难题的⽅法
				- 【撰写】：其中有少数的写作任务，可能已经测试出我想要的某些功能，我可能也把⼤致上的⽅法流程思考通顺，这时候这些写作任务，其实进入了最后撰写完成的阶段
			- 空挡时间
				- 方法
					- 第⼀步，列出⾃⼰⽇常⽣活中的情境清单；
						- 举例
							- 早起时刻（通常只有我一个人）
							- 早餐时间（通常是和老婆两人约会）
							- 工作（正常上班时间）
							- 午休（中午用餐与自己可运用时间）
							- 通勤（乘车又可自己利用的时间）
							- 晚餐（回家与老婆孩子用餐时刻）
							- 家人（陪伴家人时刻）
							- 睡前（孩子睡着后的部分时间）
					- 第⼆步，把空档情境结合⽬标，设计出情境标签
						- 举例
							- 「个人空闲」情境，把我想在一个人空档出现时做的任务放入，可能是玩一个游戏；
							- 「两人时间」情境，把我想和老婆一起看的电影、一起讨论的话题放入，当出现这样的情境时，就优先执行这样的任务；
							- 「想和孩子一起做」情境，把想要带孩子一起去的餐厅、游乐园，或是想跟孩子一起读的书，或是想跟孩子一起练习的事情，放入这个任务情境，当出现适合情境时，就优先执行这样的事情
	- 建立任务的「⾃动透视」
		- 目的
			- 直接筛选出今天在办公室可以执行的下一步行动；
			- 还是筛选出休闲时可以做的事；
			- 也可以筛选出我可以立即进行的写作任务；
		- 思路
			- 我在办公室时，应该优先执⾏的任务
			- 在适合思考的情境，快速聚焦可以使⽤的写作素材
			- 当进入休息情境，可以执⾏哪些任务？
			- 把⾏动摆在我做到某步骤就⼀定会看到他的位置，当前面的行动完成，自然就会在这个行程列表上看到下一个我必须要做的行动
	- 每周行动清单
		- 123原则
			- 每天都预先设定一个专属于自我实现的重要目标
			- 每天设定两个项目的推进进度
			- 每天早午晚三个时段，还剩下多少时间处理琐事
		- 处理步䠫（举例）
			- 先看时间提醒：有没有下周特定时间要开始的任务？
			- 看「重要」标签：哪些项目需要推进进度？
			- 看「收集箱」：有没有这周收集，还没排程处理的任务？
			- 根据已经排出来的子弹行动清单，看看有没有可以安排的特殊情境任务：空闲、两人时间、思考、测试、撰写、采买、家事
			- 看「计划」标签：有企划中项目可以推进下一步了吗？
			- 看「追踪」标签：有没有需要回顾的任务？


## 感想&疑惑

- 标签的使用方法，后序标记法
	- 在本书中描述的【目录清单】和【排程】更多侧重于分类的目标，是一种替代标签分类的更加个性化、灵活的方法。核心目标在于“以少驭多”的管理优势。因此暂时无需研究标签的”叙词法“。
- 项目行程安排时，调整OB嵌入的格式使其更加直观、格式统一（嵌入前的缩进）？
	- 若按照归类的思路理解，在当前阶段暂时不用考虑。
- 项目行程安排时具体如何进行链接？新增任务如何处理？
	- 可以将本书中的【排程】理解为更高级的归类，按照这个思路新增任务即可。新增链接任务可以使用描述性的文本串联整个项目。



