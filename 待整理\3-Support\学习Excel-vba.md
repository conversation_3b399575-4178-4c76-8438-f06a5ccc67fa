---
endDate: 2024-09-07
repeat: everyDay
---
## 概述

- 动机：过去的工作中经常碰到批量处理数据、实现一些“自动化”的需求，但总是束手无策（例如：服装类商品分类装箱……）
- 目标：掌握Excel-vba的基础知识
- 愿景：借助简单的工具，可快速编写满足需求的代码
## 行动

- [x] 制定Excel-vba视频学习计划 [^1] ⏳ 2024-08-23
	- 视频总时长32h，1.5倍速观看，21小时可看完；每天1小时，截止9月7日可学习完成
- [x] [课时1：学习Excel-vba](https://www.bilibili.com/video/BV1gr4y137WY?p=1) ⏳ 2024-08-24
- [x] [课时2：学习Excel-vba](https://www.bilibili.com/video/BV1gr4y137WY?p=22) ⏳ 2024-08-25
	- [[闪念-学习Excel-vba]]
- [x] [课时3：学习Excel-vba](https://www.bilibili.com/video/BV1gr4y137WY?p=33) ⏳ 2024-08-26
- [x] [课时4：学习Excel-vba](https://www.bilibili.com/video/BV1gr4y137WY?p=43) ⏳ 2024-08-27 
- [x] [课时5：学习Excel-vba](https://www.bilibili.com/video/BV1gr4y137WY?p=49) ⏳ 2024-08-28
- [x] [课时6：学习Excel-vba](https://www.bilibili.com/video/BV1gr4y137WY?p=58) ⏳ 2024-08-30
- [x] [课时7：学习Excel-vba](https://www.bilibili.com/video/BV1gr4y137WY?p=70) ⏳ 2024-08-31
- [x] [课时8：学习Excel-vba](https://www.bilibili.com/video/BV1gr4y137WY?p=83) ⏳ 2024-09-01
- [x] [课时9：学习Excel-vba](https://www.bilibili.com/video/BV1gr4y137WY?p=84) ⏳ 2024-09-02
- [x] [课时10：学习Excel-vba](https://www.bilibili.com/video/BV1gr4y137WY?p=90) ⏳ 2024-09-03
- [x] [课时11：学习Excel-vba](https://www.bilibili.com/video/BV1gr4y137WY?p=99) ⏳ 2024-09-04
- [x] [课时12：学习Excel-vba](https://www.bilibili.com/video/BV1gr4y137WY?p=112) ⏳ 2024-09-05
- [x] [课时13：学习Excel-vba](https://www.bilibili.com/video/BV1gr4y137WY?p=134) ⏳ 2024-09-06
- [x] [课时14：学习Excel-vba](https://www.bilibili.com/video/BV1gr4y137WY?p=143) ⏳ 2024-09-07
- [x] [课时15：学习Excel-vba](https://www.bilibili.com/video/BV1gr4y137WY?p=155) ⏳ 2024-09-08
- [x] [课时16：学习Excel-vba](https://www.bilibili.com/video/BV1gr4y137WY?p=168) ⏳ 2024-09-09
- [x] [课时17：学习Excel-vba](https://www.bilibili.com/video/BV1gr4y137WY?p=183) ⏳ 2024-09-10
- [x] [课时18：学习Excel-vba](https://www.bilibili.com/video/BV1vY411K7o1?p=1) ⏳ 2024-09-11
- [x] [课时19：学习Excel-vba](https://www.bilibili.com/video/BV1vY411K7o1?p=15) ⏳ 2024-09-12
- [x] [课时20：学习Excel-vba](https://www.bilibili.com/video/BV1vY411K7o1?p=26) ⏳ 2024-09-13
- [x] [课时21：学习Excel-vba](https://www.bilibili.com/video/BV1vY411K7o1?p=38) ⏳ 2024-09-14
- 课时1：阅读并梳理《高级VBA编程宝典(第9版)》关键知识
	- 结合已学习视频中的常用知识点
	- 以管理员身份运行“命令提示符”，`mklink /d [链接名] [目标文件夹路径]`
	- [[高级VBA编程宝典(第9版).pdf]]
- 

## 资料
```dataviewjs
	const links = dv.current().file.outlinks
	const unlinks = links.values.filter((values, index, self) => {
	  return self.findIndex((t) => t.path === values.path) === index
	})
	links.values = unlinks
	dv.list(links)
```