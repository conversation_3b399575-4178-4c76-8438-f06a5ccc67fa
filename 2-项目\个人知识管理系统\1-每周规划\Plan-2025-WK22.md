---
homePageLink: "[[PKMS-首页]]"
---
# 1. 本周方向
```dataviewjs
	// 获取当前文件的路径信息，提取项目名称
	const currentFile = dv.current().file;
	const currentPath = currentFile.path;
	const projectName = currentPath.split("/")[1].trim();
	
	//获取周数、年
	const weekNumber = Number(dv.current().file.name.split("WK")[1]) -1;
	const year = dv.current().file.name.split("-")[1];
	
	//动态生成文件名
	const dynamicFilename = `Replay-${year}-WK${weekNumber.toString().padStart(2, '0')}.md`;
	const path = `2-项目/${projectName}/3-每周复盘/${dynamicFilename}`;
	const file = app.vault.getAbstractFileByPath(path);
	const targetHeading = "# 6. 下周聚焦";
	if(file){
		//读取并解析文件
		const content = await dv.io.load(path);
		const headingRegex = new RegExp(`(${targetHeading}[^]*?\\n)([^]*?)(?=\\n#|$)`);
		const match = content.match(headingRegex);
		const result = match[2].trim();
		dv.paragraph(result)
	}else{
		dv.el("p","无内容")
	}
```


# 2.  [[字段提示信息表#周目标描述： f37322|周目标]] 

- 尝试探索并输出可行的PKMS价值工作流（项目+知识）（[KR1]）
- 尝试探索并完成PKMS项目基础组件的搭建（[KR1]）



# 3. [[字段提示信息表#验收标准描述 19887e|验收标准]] 

- [x] 输出初步[[PKMS价值流程图]]，并通过本项目的理论验证
- [x] 在obsidian中完成PKMS项目基础组件的初步搭建，并据此创建对应组件模板



# 4. 关联任务

- [x] 通过Deepseek提问、启发、再提问的方式探索可能的PKMS价值工作流
- [x] 尝试根据价值工作流初步搭建对应模板
- [x] 尝试搭建文件树框架结构
- [x] 优化PKMS项目组件模板