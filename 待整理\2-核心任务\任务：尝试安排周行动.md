---
Project: 项目：仓储工作经验总结
---
## 成果

- 探索周行动安排的工作流程
## 行动

- [x] 设计“流程进度”
	- [[“防弹笔记”实用手册]] 
	- [[情景设计#目标选择]] 
	- 生活项目的进度流程还不清楚如何设计？可能还需要积累一段时间
		- 无论工作、生活、个人项目统一使用「重点推进」、「计划酝酿」、「定期追踪」的进度流程来管理，当前情况也许行得通
	- 将行动放置并固定在右侧边栏
		- 可以将行动放置并固定在右侧边栏，最好可以动态更新，方便随时查看行动完成情况
- [x] 设计“时间情景”
	- [[行动-2024-21]] 
	- [[情景设计#时间情景]] 
	- [[《防弹笔记法》阅读笔记]] 
	- [[《大脑减压的子弹笔记术》阅读笔记]] 
	- 固定的基础工作总是被其他事情（领导安排、仓库临时事情）打断？
	- 工作任务中分不清楚哪些是现在最重要的，不知如何下手，总是被其他的事情打断，且不能清楚的区分哪些是重要的，此时应该做哪些事情。以前是工作有遗漏，现在所有的工作都记录下来了，却不知先做哪个才是对的？
	- “梳理仓储工作经验”是工作流程还是生活、学习流程？
	- 滴答清单收集箱中的任务太多，让我不知所措，不知道哪些是对我来讲最重要的，或者说是判断今天需要做什么？感觉把自己要做的事情收集之后，心中的压力变小了，后面就有理由拖一拖了？滴答清单中子任务无法关联主任务，让人很是头疼，强迫症甚至让我无法好好安排工作清单？
- [x] 安排行动
	- [x] 如何将任意行或块嵌入笔记？ ^a67949
		- `outliner.md` 插件不能满足嵌入行保留完整的信息，包括任务列表前`- []` 符号
		- 在嵌入操作时，`![[^^ ]]` 通过输入两个倒v字符，复制黏贴需要嵌入的文本行，即可显示完整的信息；嵌入的内容不可直接编辑，但可以修改任务的状态；可以通过`Ctrl+"单击嵌入链接"` 在新的页面高亮显示并定位到嵌入文本行的位置
			- 嵌入式样式不友好，嵌入内容前方有大量留白？
				- 使用自定义CSS片段处理
	- [x] 编写CSS片段
	- 周行动安排中，区分了重点项目、个人目标、其他，但针对其他分类中未将其行动对应具体的空挡情景？