---
Project:
  - 项目：仓储工作经验总结
---

> 本文由 [简悦 SimpRead](http://ksria.com/simpread/) 转码， 原文地址 [blacksmithgu.github.io](https://blacksmithgu.github.io/obsidian-dataview/)

##  数据索引

Dataview 对 Markdown 文件中的元数据进行操作。它无法读取保管库中的所有内容，而只能读取特定数据。您的某些内容（例如标签和要点（包括任务））会在 Dataview 中自动提供。您可以通过字段添加其他数据，无论是在每个 YAML Frontmatter 的文件顶部，还是通过 `[key:: value]` 语法使用内联字段在内容中间。 Dataview 对这些数据建立索引，以便您可以查询。

Dataview 索引某些信息，例如标签和列表项以及您通过字段添加的数据。 Dataview 查询中仅提供索引数据！

例如，一个文件可能如下所示：

```
---
author: "Edgar <PERSON> Poe"
published: 1845
tags: poems
---

# The Raven

Once upon a midnight dreary, while I pondered, weak and weary,
Over many a quaint and curious volume of forgotten lore—
```

 或者像这样：

```
#poems

# The Raven

From [author:: <PERSON>], written in (published:: 1845)

Once upon a midnight dreary, while I pondered, weak and weary,
Over many a quaint and curious volume of forgotten lore—
```

就索引元数据（或您可以查询的元数据）而言，它们是相同的，仅注释样式不同。如何注释元数据取决于您和您的个人喜好。使用此文件，您将拥有可用的元数据字段 `author` 以及 Dataview 自动为您提供的所有内容作为隐式字段，例如标签或注释标题。

## 隐式字段

- 页面元数据

| 字段名称             | 数据类型   | 描述                                                                                    |
| ---------------- | ------ | ------------------------------------------------------------------------------------- |
| file.starred     | 布尔值    | 如果该文件已通过 Obsidian Core 插件“书签”添加了书签                                                    |
| file.size        | number | 文件的大小（以字节为单位）                                                                         |
| file.name        | 文本     | 文件名如 Obsidians 侧栏中所示                                                                  |
| file.folder      | 文本     | 该文件所属的文件夹的路径                                                                          |
| file.path        | 文本     | 完整的文件路径，包括文件名                                                                         |
| file.ext         | 文本     | 文件类型的扩展名；一般来说 `md`                                                                    |
| file.ctime       | 日期、时间  | 文件的创建日期                                                                               |
| file.cday        | 日期、时间  | 文件的创建日期                                                                               |
| file.mtime       | 日期、时间  | 文件上次修改的日期                                                                             |
| file.mday        | 日期、时间  | 文件上次修改的日期                                                                             |
| file.day         | 日期、时间  | 仅当文件的文件名中包含日期（格式为 `yyyy-mm-dd` 或 `yyyymmdd` ）或具有 `Date` 字段/内联字段时才可用                   |
| file.link        | 列表     | 文件的链接                                                                                 |
| file.tags        | 列表     | 注释中所有唯一标签的列表。子标签按每个级别进行细分，因此 `#Tag/1/A` 将作为 `[#Tag, #Tag/1, #Tag/1/A]` 存储在列表中         |
| file.etags       | 列表     | 注释中所有显式标签的列表；与 `file.tags` 不同，不会分解子标签，即 `[#Tag/1/A]`                                  |
| file.inlinks     | 列表     | 此文件的所有传入链接的列表，表示包含此文件链接的所有文件                                                          |
| file.outlinks    | 列表     | 该文件的所有传出链接的列表，这意味着该文件包含的所有链接                                                          |
| file.aliases     | 列表     | 通过 YAML frontmatter 定义的注释的所有别名的列表                                                     |
| file.tasks       | 列表     | 该文件中所有任务的列表                                                                           |
| file.lists       | 列表     | 文件中所有列表元素的列表（包括任务）；这些元素实际上是任务，可以在任务视图中呈现                                              |
| file.frontmatter | 列表     | 包含 `key \| value` 文本值形式的所有 frontmatter 的原始值；主要用于检查原始 frontmatter 值或动态列出 frontmatter 键 |

- 任务和列表的元数据

| 字段名称 | 数据类型 | 描述  |
| ---- | ---- | --- |
|      |      |     |


## JS语法（task）

- `dv.current()`获取脚本当前正在执行的页面的页面信息

- `dv.page(路径)` 将简单路径或链接映射到完整页面对象，其中包括所有页面字段。自动进行链接解析，并自动计算出扩展名（如果不存在）
示例：
```
dv.page("books/The Raisin.md") => The page object for /books/The Raisin.md
```

- `dv.pages(来源)`采用单个字符串参数 `来源` ，其形式与查询语言源相同。返回页面对象的数据数组，这些对象是带有所有页面字段作为值的普通对象
示例：
```
dv.pages() ==>all pages
dv.pages("#books") => all pages with tag 'books'
dv.pages('"folder"') => all pages from folder "folder"
dv.pages("#yes or #no") => all pages with tag #yes, or which DON'T have tag #no
dv.pages('"folder" or #tag') => all pages with tag #tag, or from folder "folder"
```

- `dv.taskList(tasks, groupByFile)`渲染 `Task` 对象的数据视图列表，由 `page.file.tasks` 获取。默认情况下，此视图将自动按原始文件对任务进行分组。如果您显式提供 `false` 作为第二个参数，它会将它们呈现为单个统一列表
示例：
```
//List all uncompleted tasks from pages marked'#project'
dv.taskList(dv.pages("#project").file.tasks.where(t => !t.completed)) 

//List all tasks tagged with '#tag' from pages marked'#project'
dv.taskList(dv.pages("#project").file.tasks.where(t => t.text.includes("#tag"))) 
```

- `dv.date(text)`强制文本和链接到 luxon `DateTime` ；如果提供了 `DateTime` ，则原样返回
示例：
```
dv.date("2021-08-08") => DateTime for August 8th, 2021
```

- `dv.duration(text)`将文本强制转换为 luxon `Duration` ；使用与 Dataview 持续时间类型相同的解析规则
示例：
```
dv.duration("8 minutes") => Duration { 8 minutes }
dv.duration("9 hours, 2 minutes, 3 seconds") => Duration { 9 hours, 2 minutes, 3 seconds }
```

## JS语法（groupBY）

- `groupBy<U>(key:ArrayFunc<T,U>,comparator?:ArrayComparator<U>): DataArray<{ key: U; rows: DataArray<T> }>`，第一个参数为一个函数，一般返回字段名称；第二个参数为一个比较函数，默认情况下按升序排序
- 



## 使用页面属性集合DataviewJS

- 在`frontmatter`中添加页面属性
- `const folder = dv.current().folder`声明变量`folder`，并将当前页面的`folder`属性赋值给变量
	- `dv.current().folder ? dv.current().folder : ""` 使用`表达式1 ? 表达式2 ：表达式3`语法，表示：表达式1为ture，则返回表达式2，否则返回表达式3；因为`null`与空字符串不是一种数据类型
- `dv.pages('"${folder)"')`将变量`folder`作为参数使用
- `const project = dv.current().project?.length ? dv.current().project[0] : ""` 在设置`project`页面属性时，通常的数据类型时列表；在JS中实际为一个数组，`length`表示数组中值得个数，`length`表示列表（数组）中至少又1个值；`project[0]`表示取数组中的第一个值
	- `.where(p =>project ? p.project?.includes(project):true)` 表示当在页面属性设置的`project`不为空时，取填写的值，否则恒为ture（代表此语句不生效）
- `const tags = dv.current().tags` 声明变量`tags`，并将当前页面的`tags`属性赋值给变量
	- `where(p =>tags ? tags.every(tag =p.file.tags.includes("#" + tag)) : true)` 因为页面属性中的`tags`与列表相似但又又不同，它与正文中的标签不同，没有#；`tags.every()` 表示同时满足多个标签，表示“且”的含义；`tags.some()` 表示所有的标签中满足其中一个即可，表示“或”的含义

示例：

```
const settingPage = dv.current();
const folder = settingPage.folder;
const project = settingPage.project;
const search = settingPage.search;
const dueDate = settingPage.DueDate;
const completionDate = settingPage.CompletionDate;
const tags = settingPage.tags;
const includesAllTags = settingPage.includesAllTags;
const completed = settingPage.completed;
dv.taskList(
	dv.pages('"s{folder)").file.tasks
	.where(t => t.text) //排除有列表符号（有序列表、无序列表、任务列表），但后面没有文本的列表
	.where(t => t.completed =completed)
	.where(t => t.text.includes(search ? search : "")
	.where(t => {
		return project ? t.path.includes(project) : true;
	})
	.where(t => duepate ? t.due <=dueDate : true)
	.where(t => completionDate ? t.completion <=completionDate : true)
	.where(t => {
		if (includesAllTags){
			return tags ? tags.every(tag => t.tags.includes("#"+tag)) : true;
	}else{
		return tags ? tags.some(tag => t.tags.includes("#"+tag)) : true;
	}
```

```
//代码重新设计。指定固定页面
const settingPage = dv.page("0-/I0TO/Dashboard/My-DV-Dashboard-DVJS-Settings.md")
```

## 设计思路

- 实现流程：
	- 设计项目文件页面属性名称（可利用template插件在创建项目文件时自动创建）
		- `大类` ：工作、生活、个人目标
		- `进度` ：重点推进、计划酝酿、定期追踪
		- `startDate`：项目开始时间（必填）
			- 若为空，在筛选指定开始、结束时间的内容时，无论某个项目是否有结束时间，均查询不到任何结果
		- `endDate`：项目结束时间（可为空）
	- 编写dataviewjs代码分别存放`工作`、`生活`、`个人目标`查询结果
	- 创建`Project-Overview` 文件，将查询结果嵌入到三个callout中，并将他门全部存放在当前文件
	- 设计callout分栏显示的CSS代码
	- 查询显示所有项目文件的链接
		- 分栏显示内容`工作`、`生活`、`个人目标`
		- 每一栏按照`重点推进`、`计划酝酿`、`定期追踪`的进度分组显示
	- 使用`Workspaces Plus` 插件创建`计划行动` 和`日常工作`工作区
		- `计划行动` 工作区用于每周日安排下周行动。采用分屏显示方式，左侧为搜索区，右侧为计划安排区
		- `日常工作`工作区用于日常工作
