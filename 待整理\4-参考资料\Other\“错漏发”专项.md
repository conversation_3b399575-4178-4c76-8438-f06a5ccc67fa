---
Project: 项目：仓储工作经验总结
---
## 行动

- [x] 每日手工统计重点仓“错漏发”数据
	- “错漏发”在客诉层面核查数据较为复杂，且经常出现客诉端没有问题，但客户投诉的情况，最终选择了手工统计的方式
	- 仓库端会第一时间接触到客户的信息，了解错漏发的详情及数据，但是为了避免被区域追责、写改善方案或上会议解释，一般都有选择的、视情况严重程度、事情传播与否上报？
	- 区域总要求仓库必须0失误，不能出现错漏发问题，当时觉得太不现实，也一度怀疑领导的脑子是不是有问题或者针对我；现在想来可能是领导被总部盯得太紧，0失误除了指业务上，也指不能传播到总部
- [x] 审核仓库错漏发问题的原因分析、改善措施
	- 仓库的原因分析、改善措施总是泛泛而谈，根本没有找到根本原因或写清楚具体的落实步骤？
	- 仓库写的东西根本“不能看”，反馈之后还要逐字逐句的修改语病？
- [x] 日例会
	- 仓库主管、仓库负责人会议上回应错漏发产生的原因和改善方案
	- 未改善完成的每日回顾具体改善进展
- [x] 周例会
	- 周维度仓库主管、仓库负责人会议上回应错漏发产生的原因和改善进展