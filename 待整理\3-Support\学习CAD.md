---
repeat: everyThreeDays
---
## 概述

- 动机：经常在对生产线及不同功能区进行规划时（新开仓或仓库布局优化）感觉束手无策
- 目标：掌握CAD软件的基础知识，熟练使用常规操作
- 愿景：在进行仓库优化布局等相关工作时，可以对相关工作给予一定助力
## 行动

- [x] 寻找CAD合适版本的破解资源并完成安装 ⏳ 2024-08-20
- [x] 寻找教学视频资源 ⏳ 2024-08-21
- [x] 制定CAD视频学习计划⏳ 2024-08-22
	- 视频总时长15.5h，1.5倍速观看，需花费10h左右；每天学习1h，预计8月27日完成学习
- [x] [课时1：观看CAD教学视频](https://www.bilibili.com/video/BV1mV411N7ud?p=1) ⏳ 2024-08-23
- [x] [课时2：观看CAD 教学视频](https://www.bilibili.com/video/BV1mV411N7ud?p=1) ⏳ 2024-08-24
- [x] [课时3：观看CAD 教学视频](https://www.bilibili.com/video/BV1mV411N7ud?p=7) ⏳ 2024-08-25
- [x] [课时4：观看CAD 教学视频](https://www.bilibili.com/video/BV1XT4y1d7ax?p=6) ⏳ 2024-08-26
- [x] [课时5：观看CAD 教学视频](https://www.bilibili.com/video/BV1XT4y1d7ax?p=20) ⏳ 2024-08-27
	- CAD软件中的内容更多的集中在快捷键的使用，不能达成学习目标？
	- 图层是什么意思？模型视图与布局视图有什么区别，分别有什么作用？
- [x] 重新安装CAD软件 ⏳ 2024-09-05
	- 打开软件后，提示许可检出超时？
- [x] 梳理CAD软件基本常识 ⏳ 2024-09-06
	- [[CAD2020软件常识]]
- [ ] 课时1：《建筑装饰工程制图与CAD》⏳ 2024-09-17
- 学习施工图识图的基本知识
- 寻找CAD绘图练习题
## 闪念

- 
## 资料
```dataviewjs
	const links = dv.current().file.outlinks
	const unlinks = links.values.filter((values, index, self) => {
	  return self.findIndex((t) => t.path === values.path) === index
	})
	links.values = unlinks
	dv.list(links)
```