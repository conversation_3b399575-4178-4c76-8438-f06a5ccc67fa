## OP设置

- 显示
	- 布局元素-->显示可打印区域-->取消勾选
	- 布局元素-->显示图纸背景-->取消勾选
	- 窗口元素-->颜色-->二维模型空间-->统一背景、十字光标（合适的颜色）
	- 窗口元素-->颜色-->图纸/布局-->统一背景、十字光标（合适的颜色）
	- 窗口元素-->颜色-->块编辑器-->统一背景、十字光标（合适的颜色）
	- 十字光标大小-->调整至合适大小（一般20）
- 打开与保存
	- 文件保存-->文件另存为-->2007/LT2007图形（确保低版本软件可以打开图纸）
- 用户系统配置
	- window标准操作-->双击进行标记（勾选）
	- 关联标注-->使新标注可关联（勾选）
- 选择集
	- 选择集模式-->先选择后执行（勾选）
	- 选择集模式-->用Shift键添加到选择集（取消勾选）
- 配置
	- 重置（还原配置项）
## OS设置

- 捕捉与栅格
	- 启用捕捉（取消勾选）
	- 启用栅格（指背景网格线，取消勾选）
- 极轴追踪
	- 启用极轴追踪（勾选）
- 对象捕捉
	- 启用对象捕捉（勾选）
	- 启用对象捕捉追踪（勾选）
	- 对象捕捉模式（全部选择）
- 动态输入
	- 启用指针输入（勾选）
	- 可能时启用标注输入（勾选）
- 快捷特性
	- 选择时显示快捷特性选项板（取消勾选）
- 选择循环
	- 允许选择循环（取消勾选）
## 经典模式

- 新建一张空白图纸
- 关闭选项卡组（“精选应用”后空白处邮件关闭）
- 显示菜单栏（自定义快速访问工具栏-->显示菜单栏）
- 显示修改、图层、样式、特性、绘图（工具-->AutoCAD）
- 修改viewcub（右上角图标wcs右键-->viewcub设置-->viewcub大小-->“自动”取消勾选-->“微型”、“不活动时透明度”调整至最小）
- 保存工作空间（工具-->工作空间-->将当前工作空间另存为）
## 操作

- 图形选取模式
	- 从左到右（蓝色，需框住全部图形）
	- 从右到左（绿色，接触图形即可选取）
- 文件找回
	- 将.bak文件重命名为.dwg格式，重新打开即可
- 快捷键
- ![image.png|1000](https://s1.vika.cn/space/2024/09/06/32987d26754f4115a99419571c0d39b1)
## 核心概念

- 图层
	- 作用
		- 控制图形的显示
		- 控制图形的修改。把某个图层锁住，在绘图时就不用担心动了不该动的东西了。
		- 图层可以设定颜色，线型，线宽
	- 冻结：隐藏图形，不能编辑不能打印，可以减少图形加载时间
	- 锁定：图形暗显，不能编辑，但能打印，可以捕捉图形元素，常用于不想被误改图形
	- 关闭：隐藏图形，且不能编辑但能全选，不能打印
	- 0图层特性
		- 不能删除，但可以修改具体特性（颜色、线型、线宽……）
		- 在0图层绘制的图形，可以在其他图层上引用
- 模型空间与布局视图
	- 模型空间
		- 定义：模型空间是CAD软件中用于创建和编辑几何图形和对象的区域。它是设计者用来构建三维模型或二维绘图的虚拟空间。
		- 用途：在模型空间中，你可以创建精确的几何图形，如线、圆、多边形、三维实体等。这些图形的尺寸和比例是按照实际尺寸绘制的。
		- 比例：模型空间中的对象尺寸是按照1:1的比例绘制的，即实际尺寸是多少，在模型空间中就绘制多少。
	- 布局视图（Layout Space）
		- 定义：布局视图是用于设置图纸页面、打印和显示视图的区域。在布局视图中，你可以设置图纸的尺寸、比例、标题栏、图框等。
		- 用途：布局视图主要用于组织和展示模型空间中的对象，以便打印或电子发布。你可以在布局视图中创建多个视图，包括不同的比例和视图方向。
		- 比例：在布局视图中，你可以设置不同的比例来显示模型空间中的对象。这意味着同一个对象在不同的布局视图中可以有不同的显示尺寸。
	- 区别
		- 目的不同：模型空间用于设计和创建对象，而布局视图用于展示和打印这些对象。
		- 比例处理：模型空间中的对象是按照实际尺寸绘制的，而布局视图可以设置不同的比例来展示这些对象。
		- 组织方式：模型空间通常只包含设计对象，而布局视图则包含图纸的所有元素，如标题栏、图框、注释等。
	- 联系
		- 相互依赖：模型空间中的对象需要通过布局视图来展示和打印。没有布局视图，模型空间中的对象无法被正确展示和输出。
		- 视图关联：在布局视图中创建的视图是链接到模型空间中的相应对象的。在模型空间中对对象的任何更改都会反映在布局视图中的相应视图上。
		- 协同工作：设计者通常在模型空间中完成设计工作，然后在布局视图中设置图纸的展示和打印参数。