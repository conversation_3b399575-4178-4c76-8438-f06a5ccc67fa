- 相互独立的行动会在某一情景下，按照处理事情的原则在大脑中自行进行优先级排序，项目亦然
- 如果“下一步”行动并没有达到预期的结果，这个“下一步”要如何处理？是否需要进一下思考“下一步”的“下一步”？
- 针对每日都需要完成的任务需要如何安排（同时每个行动的时间也不同）？
- 日程表查询结果可以实现：已过期的日程
- 回顾日程表未来计划时，原始记录中已完成、未完成、计划执行日期的排序会很混乱，不方便查询？（将查询结果做成横向选项卡形式）
- 如果某个行动因为一些原因无法执行或完成，此行动应如何处理？
- 想法和附件是放在行动下方好？还是放在资料下面好？
- 当某个项目当前行动完成时，会继续思考并在“行动清单”中记录下一步；但在查看并执行Review-view中的行动时，感觉永远做不完；有时会搞不清楚某个项目是否已推进过？
- `Force note view mode`插件可以将dashboard视图的默认视图单独调整为阅读模式，避免编辑视图下鼠标位置影响最终渲染效果
- 在dashboard面板中，感觉行动总是做不完，需要设置截止时间吗？
- 在通过标签按照“时间”、“精力”筛选后，感觉事情还是很多，还是需要以某种方式再次排序？
- 完成一个行动时，可能半小时就完成了，也可能就是一下午，那其他的行动就无法执行了？
- 阅读后，精力不济，下一个行动不知如何安排？

```css
	//隐藏dataview查询出的任务文本后的标签
	a.tag{
	display:none
	}
```

- 使用内联字段为任务文本添加日期属性，并采用dataview查询时，需要使用标准格式“YYYY-MM-DD”
- 因为晚上耽误些时间，看剧娱乐的时间又向后拖延了30分钟
- 心里出现时问题或困扰有些事关于整个项目的，有些是在执行某个具体的行动时突然出现的，两种问题好像不是一个层面的，全部放在闪念中好像不够直观，而且项目的行动感觉不连续；也许可以将行动层面的问题直接放在行动下更直观
- 利用坚果云部署ob的移动端
- 周日上午可以安排一些简单的工作+生活琐事（例如:打扰卫生），下午安排游戏时间
- 从每天的时间安排上来看，白天时间基本都用在工作上（为了生存），下班后时间有一半是看剧（休闲娱乐），一半时间做睡觉前准备；都不知道每天生活的意义和乐趣在哪里？
- 如果每天晚上只安排不到3小时看电视剧，那如果想专心玩玩游戏，该如何安排？
- 每天看电视剧的时间即使超过3小时，熬到凌晨3-4点，自然会觉得没看够，时间太少了
- 如果每天看专心看电视剧的时间只有不到3小时，觉得今天过的很不划算，很亏
- 总是习惯一口气解决一个困扰我很久的问题
- 当前免费的ai工作并不能实现替代人工写代码，所以也不必浪费太多的时间
- 防弹笔记的每个模块并不能很顺畅的衔接？
- 当前暂时未找到更好的方式快速将flomo中的内容导入到obsidian中，而且flomo的API需要付费才能使用，后续可能有更好的方式