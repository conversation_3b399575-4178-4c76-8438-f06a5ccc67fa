---
created_Date: 2025-06-02
aliases:
  - 由于直接采用周验收标准作为成果验收内容，导致团队遗漏或忽略真实的完成情况，进而造成KR进度数据的系统性失真与周评审的形式化空转
relation:
  - 每周评审
type: 认知断层
status: 已解决
发生次数: 4
---
# 1. 基础信息

- 发生场景：每周评审
- 核心问题：不清楚如何制定验收标准？
- 关键影响：周评审流于形式

# 2. 关键记录

| 时间               | 行动               | 结果                                                                                                                                                                                                                                                                                   | 状态  | 发现/洞见 |
| ---------------- | ---------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ | --- | ----- |
| 2025-06-30 16:00 | 利用DeepSeek查找可行方法 | 验收标准<br>（1）对于缺乏认知的目标，需要采用“渐进式标准构建法”（框架→主干→协同）制定验收标准（通过分阶段制定验收标准，逐步扩展验证范围）                                                                                                                                                                                                            | 验证中 |       |
| 2025-06-28 11:30 | 利用DeepSeek查找可行方法 | 目标制定<br>（1）合格目标 = 核心价值 + 可衡量状态/可量化结果 + 时间线<br>验收标准<br>（1）当关键细节不明确时，采用「负面清单+关键行为」验证的写法                                                                                                                                                                                                | 验证中 |       |
| 2025-06-02 10:00 | 利用DeepSeek查找可行方法 | 目标制定<br>（1）量化周目标（本身含数字指标）需明确明确定义数据来源、计算方式和达标阈值<br>（2）非量化周目标（产出为文档、决策、流程或行动）用“交付物+确认方式”替代数字，确保结果可客观验证<br>（3）复杂周目标，可拆解为多项必须同时满足的条件，形成检查清单<br>验收标准制定<br>（1）完成[具体动作]，使[指标]从[当前值]达到[目标值]（数据来源：[xxx系统/报告]）<br>（2）交付[具体成果物]，并通过[验证方式]/完成[关键行动]，产生[可追溯证据]<br>（3）当关键细节不明确时，采用「负面清单+关键行为」验证的写法 | 验证中 |       |
| 2025-07-05 14:00 | 利用DeepSeek查找可行方法 | 渐进式构建法                                                                                                                                                                                                                                                                               | 验证中 |       |
# 3. 解决方案

| 日期  | 根因分析 | 解决方案 | 残留风险 | 后续行动 | 备注  |
| --- | ---- | ---- | ---- | ---- | --- |
|     |      |      |      |      |     |

