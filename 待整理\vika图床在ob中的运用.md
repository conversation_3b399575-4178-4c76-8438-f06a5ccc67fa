## Vika设置

- 在[官网](https://vika.cn/login)注册一个vika账号验证登录
- 创建一个用于存放图片的空间站（例如：图床，免费版允许创建10个空间，每个空间站有1G的存储限制）
- 新建一个空白维格表，至少保留2个字段（标题、附件）
- 创建API令牌
	- 点击左下角头像，选择“个人设置”
	- 选择“开发者配置”，创建一个令牌，复制留作后续使用
- 维格表ID
	- 选中新创建的维格表（使用默认“维格视图”），在浏览器地址栏可找到维格表ID，一般是以`dstK`开头的包含字母、数字的一串代码（例如：dstK9l07T3FAnkax0l）
	- 复制好留作后续使用
## Picgo设置

- 完成[客户端](https://github.com/Molunerfinn/PicGo) 
- 安装插件
	- 在客户端找到「插件设置」，搜索「vikadata」并安装
- 图床配置
	- 找到客户端「图床设置」-->「vika维格表」
	- 将API令牌复制到API Token
	- 将维格表ID复制到维格表ID
	- 字段名称--填写维格表中存放图片的字段名称
	- 图片上传方式--默认选择“一张图片占用一个单元格”
- 在需要使用时启动客户端或开机自启
## Obsidian设置

- 安装第三方插件[“Image auto upload Plugin”](https://github.com/renmu123/obsidian-image-auto-upload-plugin) 
- 配置插件
	- 默认上传器：Picgo(app)
	- 上传文件后删除源文件：勾选
	- 其他：默认
- 正常拖动图片、复制粘贴至obsidian均可制动上传（如需要修改上传图片的名称可能需要搭配其他插件）

