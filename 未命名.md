---
area: 
created_Date: 
update_Date:
---
- 决策目标： [要解决的核心问题，如“提升数据库查询效率”、“降低用户流失率”]  

- 上下文背景： [该决策的典型环境特征，如“高并发读写场景”、“产品新版本上线初期”]  

- 可选项对比：

| 选项 | 适用条件 | 优势 | 风险 | 推荐优先级 |
|------|----------|------|------|-----------|
| [选项A] | [触发条件，如“数据量 < 1TB”] | 开发成本低 | 扩展性差 | ⭐⭐⭐ |
| [选项B] | [触发条件，如“读操作占比 > 80%”] | 查询响应快 | 数据一致性弱 | ⭐⭐ |
| [选项C] | [触发条件，如“预算充足 + SLA要求高”] | 自动扩容 | 成本高 | ⭐⭐⭐⭐ |

- 决策规则：
*   IF [条件1] + [条件2], THEN 选择 [选项X] BECAUSE [依据，如“符合成本效益原则”]  
*   IF [条件3] BUT [限制因素], THEN 避免 [选项Y] BECAUSE [依据，如“与系统架构不兼容”]  

- 验证指标：
*   [决策后需跟踪的KPI，如“查询延迟 < 100ms”、“月度运维成本降幅 > 15%”]  

- 案例参考：
*   [成功/失败实例，如“✅ 项目X选方案B，QPS提升200%”、“❌ 项目Y误用方案A导致宕机”]  







