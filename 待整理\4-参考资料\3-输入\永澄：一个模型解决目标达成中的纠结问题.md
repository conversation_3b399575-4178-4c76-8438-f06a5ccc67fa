---
Project:
  - 项目：仓储工作经验总结
---
> 本文由 [简悦 SimpRead](http://ksria.com/simpread/) 转码， 原文地址 [mp.weixin.qq.com](https://mp.weixin.qq.com/s?__biz=MzIzOTY0OTA3OA==&mid=2247483921&idx=1&sn=5a7ee5b548f15f8aa60523da3e99e3b2)

![](http://mmbiz.qpic.cn/mmbiz_png/4huZu0wMGt95mmEXjicuoKYkBic6sfOEhb3PuX8Y4hu9XZrI748pKuhTDicYQGSKuTJiaeycSYGY5TpRop0X3jf4KA/0?wx_fmt=png)  

![](http://mmbiz.qpic.cn/mmbiz_png/4huZu0wMGt95mmEXjicuoKYkBic6sfOEhbkTE5AzicaKgRVT540uN30yTlHwJz3W7AQIGrSkhY44GMj5zG7YnueSQ/0?wx_fmt=png)  

已经有好几天没有打开微信了，不知道有多少条未读信息了，这种与世隔绝的生活还是被一个伙伴来电话给中断了。他向我问了一个关于情绪和选择的问题：“老师，我实在是太纠结了，在周度计划中，我觉得自己能够也应该多做一些任务，但是我把任务写进去的时候我就会因为时间不够而焦虑，如果不写的话，我就觉得自己碌碌无为，你说我到底该怎么办啊？”

听到这个问题，我立刻想到了 **RSQC 模型**，它包含了解决此类问题的所有思路。果不其然，当我按照 RSQC 的方式向他提问之后，问题迎刃而解。

RSQC 模型，我还用来解决另外一类很常见的问题：**因为盲目自大而导致的计划失败问题**。我们还是来看一个实例吧：

俱乐部有个伙伴，他在开始成长的初期，生怕时间不够用，于是给自己安排了每天早晨 4 点起床，读书 1 小时、锻炼 30 分钟、背英语 30 分钟、绘制流程图 1 小时、做饭 1 小时、洗漱 15 分钟，到了八点再去上班。可是，很快他就遇到了掉头发的毛病——斑秃，去医院检查之后，医生说睡觉太少了。

他很郁闷，明明想要提升，但是却被身体的基础条件所限制。于是他就去问自己的导师，导师回答他：“既然要多睡会，那就少做点吧？” 一个多么简单的道理，当时他就想不通！现在，他已经弄得很清楚了，可以把握轻重缓急，可以从容面对持续成长的过程。如果当时他知道 RSQC 的话，就不用走那么多弯路了。

对了，他就是我。

![](http://mmbiz.qpic.cn/mmbiz_png/4huZu0wMGt95mmEXjicuoKYkBic6sfOEhb0bto5mtEOmKpsgcsEs2A7TNDprONEAK64GgeTicOq6l4p006xTg2DIA/0?wx_fmt=png)  

我很喜欢做模型的，RSQC 就是我自造模型的一款，它代表了四个单词：

• Range：范围

• Schedule：进度

• Quality：质量

• Cost：成本

很多人都知道 SMART 这个目标工具，但是，RSQC 作为 SMART 的前置工具，很多人都是一抹黑。RSQC 也是一款目标制定的自检工具，只要有它，你就可以判断自己目标设定得是否合理，我们试着看一个范例：本周内，写一个运营人员持续积累的方案。

要知道，很多老板就是这样下达指令的。如果你没有若干工具支撑，估计你的做法很可能是在周度计划中写下这个任务，然后就去执行了。在执行的过程中遇到挫败就会拖延，然后认为想办法糊弄老板算了。如果你有 RSQC 工具，很可能你会拿出一张纸，写下：

• 范围：书写一个方案，这个方案是关于运营人员持续积累方法的。

• 进度：本周末内完成。

• 质量：未获得明确的要求。

• 成本：执行者的时间。

通过 RSQC 工具的反推思考，你会发现这个目标中缺乏了对于质量的描述。也就是说如果可以糊弄的话，这个方案很可能一天就搞定了，但也可能为了达到世界一流标准（质量），而导致进度上无法保证。

所以，这时你要再向老板提问：“老板，你看，我大约要做到什么质量标准呢？” 老板就会给你一个判断的方向，如果他做不到的话也没有问题，你要根据自己的能力情况判断具体质量，毕竟老板要求的是周末提交（进度，你要根据进度来设置质量目标）。

看看，当脑海中有一个模型，你的工作思路和可靠性就和之前完全不同了。

![](http://mmbiz.qpic.cn/mmbiz_png/4huZu0wMGt95mmEXjicuoKYkBic6sfOEhbRmuHuNt1KEB2Y8ZoZ6lpHcwebZEebEOFgCFibAzwicNSlY8ibLGjJaF3g/0?wx_fmt=png)  

你可以把 RSQC 这个模型理解为四个要素相互联动关系，**如果你期待让目标发生变化，那就必须要考虑这四个要素的配比**，来看看下图：

![](http://mmbiz.qpic.cn/mmbiz_png/4huZu0wMGt8IVFBEUIibguEowl96ux0lNqAWxpOYE2epTzgyic3kWoY3vujmCCE8icrInYSMO7nMwZF0aOjAKn32w/0?wx_fmt=gif)  

假如你期待在这个目标里多做一些事（范围增加），那么，就要考虑进度是否延长、质量是否需要降低、成本是否要更多投入。如果有一些值是限定的，比如说必须要保质保量、完成任务的时间也不能调整，那么目标可以达成的唯一方式就是：增加成本投入。

**范围、进度、质量、成本是目标的基本要素，目标的设定和变化都要思考这四个值，就可以避免自己掉入到情绪之中**。我们来看看例子：

年目标课里有个学员，他很努力地在研究机器学习领域，但是他就遇到了困扰：又要掌握机器学习这个学科知识，又要跟我一起学自我管理的整个体系，但是由于时间不够，他就很焦虑，不知道到底应该怎么办了。

我就使用 RSQC 工具帮助他分析了一下，我们最后发现，他的客观情况如下：

• R - 范围：想要学习两个内容，分别是机器学习和自我管理

• S - 进度：希望 3 个月内都完成掉

• Q - 质量：希望以自己最高标准来做

• C - 成本：自己下班后的业余时间

所谓的焦虑，就是因为知道自己做不到而产生的一种情绪。所以，我就向他询问几个问题：①范围能不能调整，他明确两个都想要；②进度能不能调整，他想了想是可以的；③质量能不能调整，他不同意，因为他说高标准能够带来更大发展；④成本能不能调整，他说自己的工作也要干好，能够投入的时间只有下班时间。

好了，经过分析，你会发现范围、质量、成本都是不许变得，那么唯一可以变化的就是进度。所以，我们最后确认的进度是从 3 个月改为一年半，他的焦虑情绪就完全解决掉了。

![](http://mmbiz.qpic.cn/mmbiz_png/4huZu0wMGt95mmEXjicuoKYkBic6sfOEhbdHZk83kAtKrGG9SQzukhIjzkFOacMkUNIdD5AVfibCpq5J8b1411tBQ/0?wx_fmt=png)  

RSQC 这个模型，抓住了目标的组成要素，四个要素彼此关联形成动态整体。如果你期待要调整一个参数，其他的三个必须要跟着变化，如果其他参数不能调整，那唯一能调的就是自己的期待。

这个工具是每个目标达成者的必备工具，必备程度打分为 10 分，也就是你必须要掌握它的使用方式。为了帮你用好这个工具，我给你一个场景和一套话术，让这个工具发挥更大作用。

场景：在制定完某个目标之后，或者推进目标过程中被挫败感卡住，不想前行。

话术：

• 请把你的目标说给我听听，关于目标的范围、进度、质量和成本，你是如何期待的呢？

• RSQC 的四个元素中，我是否都能保证自己可以达成，尤其是保进度、达质量和省成本这三个值，能不能变化？

• 判断哪个元素是可以变动和调整的？你要如何做出目标的调整来保证目标的达成呢？

• 请重新描述你的目标吧。

请你试试看吧，我们今天只是先带个头，让你先了解一下目标组成的四大元素，并大致了解这些元素之间的限制关系。可是，RSQC 并没有想象中那么简单，如果没有持续的练习，估计你无法判断自己目标推进过程中，究竟哪里出了问题，那么，所谓的 “目标达成” 就是扯的了。未来，我会持续讲解这个模型的应用，确保你的目标不至于在设定之初就是错的！

**附**

昨天，我的全家七口和团队伙伴们一起吃饭了，这仿佛成了我们的一个传统：我的父母和丈人丈母娘，向我们的整个团队表达祝贺、祝福和关怀，我们都很开心。开心的前提是闭关了这么久，我们很多问题都已经解决，大家也开始对接下来更有期待！

昨天的互动奖我想送给**从头越 15**，如果你看他给我的留言，你会构建出一个形象：缠论爱好者、结构化能力很强、看问题很准、知识很多、能给人若干侧面的参考。他在近一段时间里，告诉我了几个核心点，比如：①看看水就知道了，它一直都是按照阻力最小的路径流动，关键是如何为能量设置结构？②学习往深里研究，操作要从容易的地方入手等等。