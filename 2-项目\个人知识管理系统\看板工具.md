---
deadline: 2025-07-01
---
# 5. 本周障碍统计
```dataviewjs
	// 获取当前文件的 deadline 属性
	const currentFile = dv.current();
	const deadline = currentFile?.deadline;
	const projectName = currentFile.file.path.split("/")[1];
	const path = `3-过程资产/${projectName}`;
	
	if (!deadline) {
	    dv.paragraph("⚠️ 当前文件缺少 deadline 属性");
	} else {
	    // 计算本周时间范围
	    const deadlineDate = dv.date(deadline);
	    const weekStart = deadlineDate.startOf('week');
	    const weekEnd = deadlineDate.endOf('week');
	    
	    // 格式化日期范围字符串
	    const dateRange = `${weekStart.toFormat("yyyy-MM-dd")} 至 ${weekEnd.toFormat("yyyy-MM-dd")}`;
	    
	    // 创建日期解析函数
	    const parseDate = (value) => {
	        if (value?.isValid) return value; 
	        const dateStr = value?.toString() || "";
	        const match = dateStr.match(/(\d{1,2}) (\d{2}), (\d{4})/);
	        if (match) {
	            const [, month, day, year] = match;
	            return dv.date(`${year}-${month.padStart(2, '0')}-${day}`);
	        }
	        return dv.date(dateStr);
	    };
	    
	    // 获取本周创建的所有障碍
	    const weekFiles = dv.pages(`"${path}"`)
	        .filter(p => {
	            if (!p.file.name.match(/^blocker-\d{8}-\d{2}$/)) return false;
	            
	            const createdDate = parseDate(p.created_Date);
	            if (!createdDate?.isValid) return false;
	            
	            return createdDate >= weekStart && createdDate <= weekEnd;
	        })
	        .sort(p => p.created_Date, 'asc'); // 按创建日期排序
	    
	    // 显示结果
	    if (weekFiles.length > 0) {
	        dv.el("p", `本周新增 ${weekFiles.length} 条障碍 (${dateRange})`);
	        
	        // 创建列表显示别名+状态+relation
	        for (let file of weekFiles) {
	            // 获取第一个别名（如果存在）
	            const alias = file.aliases?.length ? 
	                (Array.isArray(file.aliases) ? file.aliases[0] : file.aliases) : 
	                file.file.name;
	            
	            // 处理状态和类型
	            const status = file.status || "未设置";
	            const type = file.type || "未定义";
	            const relation = file.relation ? file.relation : "未设置";
	            // 创建列表项
	            dv.paragraph(`- [[${file.file.path}|${alias}]] ` +`🚦${status} ` + `🔗${relation} ` +`🔤${type}`)}
	    } else {
	        dv.paragraph(`📭 本周 (${dateRange}) 未创建任何障碍记录`);
	    }
	}
```

^5b7c96
