---
homePageLink: "[[PKMS-首页]]"
planLink: "[[Plan-2025-WK24]]"
---
# 1. 任务安排
> [!dashboard]
> 
> > [!todo] 今日代办
> > ```tasks
> > hide backlink
> > filter by function \
> > const fileName = query.file.filenameWithoutExtension;\
> > const dateMatch = fileName.match(/\d{4}-\d{2}-\d{2}/);\
> > if (!dateMatch) return false;\
> > const targetDate = moment(dateMatch[0]);\
> > const year = targetDate.isoWeekYear();\
> > const weekNumber = targetDate.isoWeek().toString().padStart(2, '0');\
> > const targetPath = `Plan-${year}-WK${weekNumber}`;\
> > const pathMatches = task.file.path.includes(targetPath);\
> > const headingMatches = task.heading === "4. 关联任务";\
> > let dateMatches = false;\
> > if (task.scheduled && task.scheduled.moment) {\
> > dateMatches = task.scheduled.moment.isSame(targetDate, 'day')}\
> > return pathMatches && headingMatches && dateMatches;
> > ```
> 
> > [!tip] 未规划
> > ```tasks
> > not done
> > no scheduled date
> > hide backlink
> > filter by function \
> > const fileName = query.file.filenameWithoutExtension;\
> > const dateMatch = fileName.match(/\d{4}-\d{2}-\d{2}/);\
> > if (!dateMatch) return false;\
> > const targetDate = moment(dateMatch[0]);\
> > const year = targetDate.isoWeekYear();\
> > const weekNumber = targetDate.isoWeek().toString().padStart(2, '0');\
> > const targetPath = `Plan-${year}-WK${weekNumber}`;\
> > const pathMatches = task.file.path.includes(targetPath);\
> > const headingMatches = task.heading === "4. 关联任务";\
> > const hasContent = task.description?.trim().length > 0;\
> > return pathMatches && headingMatches && hasContent;
> > ```

# 2. 阻碍/[[字段提示信息表#技术债 7ffbc6|技术债]] 

- 在excallidraw中绘图时，需要重复进行手动布局调整（是否可以优化`Mindmap format`脚本以实现需求） #阻碍 
- 在excallidraw中绘图时，频繁点击鼠标来使用“编辑”功能给工作带了困扰 #阻碍 
- 管理（记录并链接）任务执行期间的[输出]的具体形式暂未确认 #技术债 
- 若已规划今日需完成的任务由于某些原因（阻碍）导致无法完整或部分完成，那么该任务的后续处理流程是什么? #技术债 
- [每日执行]页面中的“任务安排”模块，经常会因误触导致而显示原始代码块 #技术债 
- 在记录（描述）技术债时尝尝不确定是否描述准确无误 #技术债 


# 2. 输出

- [[知识管理价值流程图]] 