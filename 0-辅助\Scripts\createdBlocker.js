// 获取项目信息的公共函数
async function getProjectInfo(tp) {
  const activeFile = tp.file.find_tfile(tp.file.title);
  if (!activeFile) {
    new Notice("未找到当前活动文件");
    return null;
  }

  const currentPath = activeFile.path;
  const projectName = currentPath.split("/")[1];
  const targetDir = `3-过程资产/${projectName}/阻碍`;

  return { activeFile, currentPath, projectName, targetDir };
}

// 确保目录存在的公共函数
async function ensureDirectoryExists(dirPath) {
  const folder = app.vault.getAbstractFileByPath(dirPath);
  if (!folder) {
    await app.vault.createFolder(dirPath);
  }
}

module.exports = async (tp) => {
  // 弹出选择框让用户选择操作
  const choice = await tp.system.suggester(
    ["新建阻碍", "定位阻碍"],
    ["create", "locate"],
    false,
    "请选择操作："
  );

  if (!choice) {
    new Notice("操作已取消");
    return ""; // 返回空字符串避免插入错误信息
  }

  if (choice === "locate") {
    await locateBlocker(tp);
    return ""; // 返回空字符串避免插入错误信息
  }

  // 新建阻碍功能
  const projectInfo = await getProjectInfo(tp);
  if (!projectInfo) return ""; // 返回空字符串避免插入错误信息

  const { targetDir } = projectInfo;
  await ensureDirectoryExists(targetDir);

  // 生成文件名：blocker-YYYYMMDD-序号.md
  const today = new Date();
  const dateStr =
    today.getFullYear().toString() +
    (today.getMonth() + 1).toString().padStart(2, "0") +
    today.getDate().toString().padStart(2, "0");

  // 查找当天已存在的障碍日志文件，确定序号
  const existingFiles = app.vault
    .getFiles()
    .filter(
      (file) =>
        file.path.startsWith(targetDir) &&
        file.name.startsWith(`blocker-${dateStr}-`) &&
        file.name.endsWith(".md")
    );

  const nextNumber = existingFiles.length + 1;
  const fileName = `blocker-${dateStr}-${nextNumber
    .toString()
    .padStart(2, "0")}.md`;
  const filePath = `${targetDir}/${fileName}`;

  // 读取模板文件内容
  const templatePath = "0-辅助/Templater/Notes/TP-Project-阻碍.md";
  const templateFile = app.vault.getAbstractFileByPath(templatePath);

  if (!templateFile) {
    new Notice(`模板文件不存在: ${templatePath}`);
    return;
  }

  const templateContent = await app.vault.read(templateFile);

  // 弹出对话框让用户输入别名
  const aliasInput = await tp.system.prompt(
    "请输入障碍日志的别名（可选）：",
    "",
    false
  );

  // 处理模板内容，替换日期等变量
  let content = templateContent;

  // 替换创建日期
  const createdDate = today.toISOString().split("T")[0];
  content = content.replace(
    'created_Date: ""',
    `created_Date: "${createdDate}"`
  );

  // 替换别名
  if (aliasInput && aliasInput.trim() !== "") {
    content = content.replace("aliases: ", `aliases: ["${aliasInput.trim()}"]`);
  }

  // 设置状态为"进行中"
  content = content.replace("status: 进行中/已关闭", "status: 进行中");

  // 创建新文件
  const newFile = await app.vault.create(filePath, content);

  // 在新标签页中打开新创建的文件
  const leaf = app.workspace.getLeaf(true);
  await leaf.openFile(newFile);
  return newFile;
};

// 定位阻碍功能
async function locateBlocker(tp) {
  const projectInfo = await getProjectInfo(tp);
  if (!projectInfo) return ""; // 返回空字符串避免插入错误信息

  const { targetDir } = projectInfo;
  const dashboardPath = `${targetDir}/dashboard.md`;

  // 确保目录存在
  await ensureDirectoryExists(targetDir);

  // 检查 dashboard.md 文件是否存在，如果不存在则创建
  let dashboardFile = app.vault.getAbstractFileByPath(dashboardPath);
  if (!dashboardFile) {
    const templatePath = "0-辅助/Templater/Notes/TP-blocker-dashboard.md";
    const templateFile = app.vault.getAbstractFileByPath(templatePath);

    if (!templateFile) {
      new Notice(`Dashboard模板文件不存在: ${templatePath}`);
      return ""; // 返回空字符串避免插入错误信息
    }

    const templateContent = await app.vault.read(templateFile);
    dashboardFile = await app.vault.create(dashboardPath, templateContent);
    new Notice(`已创建 Dashboard 文件: ${dashboardPath}`);
  }

  // 在新标签页中打开 dashboard 文件
  const leaf = app.workspace.getLeaf(true);
  await leaf.openFile(dashboardFile);
  new Notice(`已打开文件: ${dashboardFile.basename}`);

  return ""; // 返回空字符串避免插入错误信息
}
