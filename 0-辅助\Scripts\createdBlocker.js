module.exports = async (tp) => {
  // 弹出选择框让用户选择操作
  const choice = await tp.system.suggester(
    ["新建阻碍", "定位阻碍"],
    ["create", "locate"],
    false,
    "请选择操作："
  );

  if (!choice) {
    new Notice("操作已取消");
    return;
  }

  if (choice === "locate") {
    // 定位阻碍功能
    await locateBlocker(tp);
    return;
  }

  // 获取当前活动文件
  const activeFile = tp.file.find_tfile(tp.file.title);
  if (!activeFile) {
    new Notice("未找到当前活动文件");
    return;
  }

  // 获取当前文件路径
  const currentPath = activeFile.path;
  // 从路径中提取项目名称
  const projectName = currentPath.split("/")[1];

  // 构建目标目录路径
  const targetDir = `3-过程资产/${projectName}/阻碍`;

  // 检查目标目录是否存在，如果不存在则创建
  const targetFolder = app.vault.getAbstractFileByPath(targetDir);
  if (!targetFolder) {
    await app.vault.createFolder(targetDir);
  }

  // 生成文件名：blocker-YYYYMMDD-序号.md
  const today = new Date();
  const dateStr =
    today.getFullYear().toString() +
    (today.getMonth() + 1).toString().padStart(2, "0") +
    today.getDate().toString().padStart(2, "0");

  // 查找当天已存在的障碍日志文件，确定序号
  const existingFiles = app.vault
    .getFiles()
    .filter(
      (file) =>
        file.path.startsWith(targetDir) &&
        file.name.startsWith(`blocker-${dateStr}-`) &&
        file.name.endsWith(".md")
    );

  const nextNumber = existingFiles.length + 1;
  const fileName = `blocker-${dateStr}-${nextNumber
    .toString()
    .padStart(2, "0")}.md`;
  const filePath = `${targetDir}/${fileName}`;

  // 读取模板文件内容
  const templatePath = "0-辅助/Templater/Notes/TP-Project-阻碍.md";
  const templateFile = app.vault.getAbstractFileByPath(templatePath);

  if (!templateFile) {
    new Notice(`模板文件不存在: ${templatePath}`);
    return;
  }

  const templateContent = await app.vault.read(templateFile);

  // 弹出对话框让用户输入别名
  const aliasInput = await tp.system.prompt(
    "请输入障碍日志的别名（可选）：",
    "",
    false
  );

  // 处理模板内容，替换日期等变量
  let content = templateContent;

  // 替换创建日期
  const createdDate = today.toISOString().split("T")[0];
  content = content.replace(
    'created_Date: ""',
    `created_Date: "${createdDate}"`
  );

  // 替换别名
  if (aliasInput && aliasInput.trim() !== "") {
    content = content.replace("aliases: ", `aliases: ["${aliasInput.trim()}"]`);
  }

  // 设置状态为"进行中"
  content = content.replace("status: 进行中/已关闭", "status: 进行中");

  // 创建新文件
  const newFile = await app.vault.create(filePath, content);

  // 在新标签页中打开新创建的文件
  const leaf = app.workspace.getLeaf(true);
  await leaf.openFile(newFile);
  return newFile;
};

// 定位阻碍功能
async function locateBlocker(tp) {
  // 获取当前活动文件
  const activeFile = tp.file.find_tfile(tp.file.title);
  if (!activeFile) {
    new Notice("未找到当前活动文件");
    return;
  }

  // 获取当前文件路径
  const currentPath = activeFile.path;
  // 从路径中提取项目名称
  const projectName = currentPath.split("/")[1];

  // 动态构建阻碍目录路径
  const blockerDir = `3-过程资产/${projectName}/阻碍`;
  const dashboardPath = `${blockerDir}/dashboard.md`;

  // 检查目标目录是否存在，如果不存在则创建
  const blockerFolder = app.vault.getAbstractFileByPath(blockerDir);
  if (!blockerFolder) {
    await app.vault.createFolder(blockerDir);
  }

  // 检查 dashboard.md 文件是否存在，如果不存在则创建
  let dashboardFile = app.vault.getAbstractFileByPath(dashboardPath);
  if (!dashboardFile) {
    // 读取模板文件内容
    const templatePath = "0-辅助/Templater/Notes/TP-blocker-dashboard.md";
    const templateFile = app.vault.getAbstractFileByPath(templatePath);

    if (!templateFile) {
      new Notice(`Dashboard模板文件不存在: ${templatePath}`);
      return;
    }

    const templateContent = await app.vault.read(templateFile);

    // 创建 dashboard.md 文件
    dashboardFile = await app.vault.create(dashboardPath, templateContent);
    new Notice(`已创建 Dashboard 文件: ${dashboardPath}`);
  }

  // 在新标签页中打开选中的文件
  const leaf = app.workspace.getLeaf(true);
  await leaf.openFile(dashboardFile);

  new Notice(`已打开文件: ${dashboardFile.basename}`);
}
