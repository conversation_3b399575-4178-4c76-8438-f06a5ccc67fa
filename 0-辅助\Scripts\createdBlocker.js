module.exports = async (tp) => {
  // 获取当前活动文件
  const activeFile = tp.file.find_tfile(tp.file.title);
  if (!activeFile) {
    new Notice("未找到当前活动文件");
    return;
  }

  // 获取当前文件路径
  const currentPath = activeFile.path;
  // 从路径中提取项目名称
  const projectName = currentPath.split("/")[1];

  // 构建目标目录路径
  const targetDir = `3-过程资产/${projectName}/阻碍`;

  // 检查目标目录是否存在，如果不存在则创建
  const targetFolder = app.vault.getAbstractFileByPath(targetDir);
  if (!targetFolder) {
    await app.vault.createFolder(targetDir);
  }

  // 生成文件名：blocker-YYYYMMDD-序号.md
  const today = new Date();
  const dateStr =
    today.getFullYear().toString() +
    (today.getMonth() + 1).toString().padStart(2, "0") +
    today.getDate().toString().padStart(2, "0");

  // 查找当天已存在的障碍日志文件，确定序号
  const existingFiles = app.vault
    .getFiles()
    .filter(
      (file) =>
        file.path.startsWith(targetDir) &&
        file.name.startsWith(`blocker-${dateStr}-`) &&
        file.name.endsWith(".md")
    );

  const nextNumber = existingFiles.length + 1;
  const fileName = `blocker-${dateStr}-${nextNumber
    .toString()
    .padStart(2, "0")}.md`;
  const filePath = `${targetDir}/${fileName}`;

  // 读取模板文件内容
  const templatePath = "0-辅助/Templater/Notes/TP-Project-障碍日志.md";
  const templateFile = app.vault.getAbstractFileByPath(templatePath);

  if (!templateFile) {
    new Notice(`模板文件不存在: ${templatePath}`);
    return;
  }

  const templateContent = await app.vault.read(templateFile);

  // 弹出对话框让用户输入别名
  const aliasInput = await tp.system.prompt(
    "请输入障碍日志的别名（可选）：",
    "",
    false
  );

  // 处理模板内容，替换日期等变量
  let content = templateContent;

  // 替换创建日期
  const createdDate = today.toISOString().split("T")[0];
  content = content.replace(
    'created_Date: ""',
    `created_Date: "${createdDate}"`
  );

  // 替换别名
  if (aliasInput && aliasInput.trim() !== "") {
    content = content.replace("aliases: ", `aliases: ["${aliasInput.trim()}"]`);
  }

  // 设置状态为"新建"
  content = content.replace(
    "status: 新建/进行中/已解决/已验证/已关闭",
    "status: 新建"
  );

  // 创建新文件
  const newFile = await app.vault.create(filePath, content);

  // 在新标签页中打开新创建的文件
  const leaf = app.workspace.getLeaf(true);
  await leaf.openFile(newFile);
  return newFile;
};
