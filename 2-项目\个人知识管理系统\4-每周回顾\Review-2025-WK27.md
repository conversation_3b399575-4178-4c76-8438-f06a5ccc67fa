---
homePageLink: "[[PKMS-首页]]"
---
# 1. 类型分析
> [!dashboard]
> 
> > [!tip] 阻碍
> >```dataviewjs
> >// 定义目标文件夹和文件名格式
> >const projectName = dv.current().file.path.split("/")[1];
> >const folder = `3-过程资产/${projectName}/阻碍`;
> >const pattern = /^blocker-\d{8}-\d{2}$/;
> >const allFiles = dv.pages(`"${folder}"`);
> >const combinedType = "";
> >// 筛选符合文件名格式且状态非"已关闭"的文件
> >const filteredFiles = allFiles.filter(p => {
> >    const name = p.file.name.replace(/\.md$/, ""); 
> >    return pattern.test(name) && p.status !== "已关闭";
> >});
> >if (filteredFiles.length === 0) {
> >    dv.paragraph("没有找到符合条件的阻碍文件");
> >} else {
> >    const specialFiles = filteredFiles.filter(p => !p.type || p.type === combinedType);
> >	// 输出特殊文件（无type或type为组合值）
> >    if (specialFiles.length > 0) {
> >        dv.el("p",`当前共计 **${specialFiles.length}** 个文件的类型未被定义或无效`)
> >        dv.paragraph(specialFiles.map(p => `[[${p.file.path}|${p.aliases}]]`));
> >    }else{
> >	    const groups = {};
> >	    filteredFiles.forEach(p => {
> >	        const type = p.type || "未分类";
> >	        if (!groups[type]) groups[type] = [];
> >	        groups[type].push(p);
> >	    });
> >	    // 按数量排序
> >	    const sortedGroups = Object.entries(groups)
> >	        .map(([type, files]) => ({
> >	            type,
> >	            count: files.length,
> >	            files
> >	        }))
> >	        .sort((a, b) => b.count - a.count);
> >	    // 取前三组
> >	    const topGroups = sortedGroups.slice(0, 3);
> >	    // 显示分组结果（列表形式）
> >	    topGroups.forEach(group => {
> >	        dv.el("p", `${group.type} (${group.count})`);
> >	        dv.paragraph(group.files.map(p => { return `[[${p.file.path}|${p.aliases}]]`}));
> >	    });
> >    }
> >}
> >```
> 
> > [!warning] 技术债
> > ```dataviewjs
> >// 定义目标文件夹和文件名格式
> >const projectName = dv.current().file.path.split("/")[1];
> >const folder = `3-过程资产/${projectName}/技术债`;
> >const pattern = /^td-\d{8}-\d{2}$/;
> >const allFiles = dv.pages(`"${folder}"`);
> >const combinedType = "阻塞型/成本型/战略型/无害型";
> >// 筛选符合文件名格式且状态非"已关闭"的文件
> >const filteredFiles = allFiles.filter(p => {
> >    const name = p.file.name.replace(/\.md$/, ""); 
> >    return pattern.test(name) && p.status !== "已关闭";
> >});
> >if (filteredFiles.length === 0) {
> >    dv.paragraph("没有找到符合条件的技术债文件");
> >} else {
> >    const specialFiles = filteredFiles.filter(p => !p.type || p.type === combinedType);
> >	// 输出特殊文件（无type或type为组合值）
> >    if (specialFiles.length > 0) {
> >        dv.el("p",`当前共计 **${specialFiles.length}** 个文件的类型未被定义或无效`)
> >        dv.paragraph(specialFiles.map(p => `[[${p.file.path}|${p.aliases}]]`));
> >    }else{
> >	    const groups = {};
> >	    filteredFiles.forEach(p => {
> >	        const type = p.type || "未分类";
> >	        if (!groups[type]) groups[type] = [];
> >	        groups[type].push(p);
> >	    });
> >	    // 按数量排序
> >	    const sortedGroups = Object.entries(groups)
> >	        .map(([type, files]) => ({
> >	            type,
> >	            count: files.length,
> >	            files
> >	        }))
> >	        .sort((a, b) => b.count - a.count);
> >	    // 取前三组
> >	    const topGroups = sortedGroups.slice(0, 3);
> >	    // 显示分组结果（列表形式）
> >	    topGroups.forEach(group => {
> >	        dv.el("p", `${group.type} (${group.count})`);
> >	        dv.paragraph(group.files.map(p => {return `[[${p.file.path}|${p.aliases}]]`}));
> >	    });
> >    }
> >}
> > ```

# 2. 流程改善

| 来源                      | 根因分析 | 改善行动                                                                                             | 验收标准                                         | 改善结果               |
| ----------------------- | ---- | ------------------------------------------------------------------------------------------------ | -------------------------------------------- | ------------------ |
| [[blocker-20250707-02]] |      | 1、对阻碍进行分类（认知断层、机制缺失、技术负债）<br>2、综合发生频率、影响程度、解决成本评估高价值阻碍，选择1-2条阻碍进行分析<br>3、根据具体阻碍制定预防性措施<br>4、跟踪验证 | 1、周回顾改善分析工作流是否中断<br>2、制定的改善行动是否短期有效（下周改善分析时） | 周回顾期间依然无法制定有效的改善措施 |
# 3. 改善回顾

```dataviewjs
// 汇总表格数据到当前页面
const projectName = dv.current().file.path.split("/")[1].trim();
const targetFolder = `2-项目/${projectName}/4-每周回顾`;
const tableTitlePattern = "流程改善"; // 标题中包含的关键词

// 1. 从当前文件名确定周数
const currentFileName = dv.current().file.name;
const weekMatch = currentFileName.match(/^Review-(\d{4})-WK(\d{2})$/);
if (!weekMatch) {
    dv.el("p", "⚠️ 模板文件中查询不生效");
    return;
}

const targetYear = parseInt(weekMatch[1]);
const targetWeek = parseInt(weekMatch[2]);	
let allTableData = [];

// 2. 处理小于目标周数的所有Review文件
for (let file of dv.pages(`"${targetFolder}"`).file) {
    const fileMatch = file.name.match(/^Review-(\d{4})-WK(\d{2})$/);
    if (!fileMatch) continue;
    
    const fileYear = parseInt(fileMatch[1]);
    const fileWeek = parseInt(fileMatch[2]);

    // 仅处理周数严格小于当前的文件
    if (fileYear < targetYear || 
       (fileYear === targetYear && fileWeek < targetWeek)) {
        
        const content = await dv.io.load(file.path);
        
        // 识别指定标题下的内容区域
        const headingRegex = new RegExp(
            `(?:^|\\n)#+\\s*.*${tableTitlePattern}.*[^\\n]*\\n([\\s\\S]*?)(?=\\n#|$)`, 
            "i"
        );
        
        const match = content.match(headingRegex);
        if (!match || !match[1]) continue;
        
        const sectionContent = match[1].trim();
        
        // 提取表格数据
        const tableRegex = /^\s*\|(.+)\|\n\s*\|?\s*:?-+:?\s*\|.+\n((?:\s*\|.+\|\n?)+)/gms;
        let tableMatch;
        
        while ((tableMatch = tableRegex.exec(sectionContent)) !== null) {
            const headerRow = tableMatch[1].split('|')
                .map(cell => cell.trim()).filter(Boolean);
            let dataRows = tableMatch[2].split('\n')
                .filter(row => row.includes('|') && !row.startsWith('|-'))
                .map(row => 
                    row.split('|')
                    .slice(1, -1)
                    .map(cell => cell.trim())
                );
            
            // 关键修改：过滤掉内容为空的表格行
            dataRows = dataRows.filter(row => row.some(cell => cell !== ""));
            
            // 严格检查数据行存在且至少有1行数据
            if (headerRow.length > 1 && dataRows.length > 0) {
                // 创建文件链接
                const weekTag = `${fileYear}-WK${fileWeek.toString().padStart(2,'0')}`;
                const fileName = `Review-${weekTag}.md`;
                const fileLink = dv.fileLink(`${targetFolder}/${fileName}`, false, weekTag);
                
                // 仅在首次发现有效表格时添加表头
                if (allTableData.length === 0) {
                    allTableData.push(["周", ...headerRow]);
                }
                
                // 添加数据行
                dataRows.forEach(row => {
                    allTableData.push([fileLink, ...row]);
                });
            }
        }
    }
}	    
if (allTableData.length > 0) {
    dv.table(allTableData[0], allTableData.slice(1));
} else {
    dv.el("p", "🎉 无历史流程改善数据");
}
```

# 4. 成功经验

| 超预期成果 | 根因分析 | 关键行为                                                  | 证据链 | 经验封装 | 复用场景 |
| ----- | ---- | ----------------------------------------------------- | --- | ---- | ---- |
|       |      | 工作方式错误（执行任务前习惯直接干，不会思考为什么干，导致范围蔓延；目标虽然完成了，但是花费了额外的时间） |     |      |      |
