---
repeat: everyTwodays
---
## 概述

- 动机：工作和生活中总是感觉自己有很多事情要做、很多问题要处理，感觉自己稀里糊涂的而且很混乱
- 目标：提升工作效率；摆脱混乱、糟糕的生活状态；把控生活、提升幸福感
- 愿景：在obsidian中实现GTD横向、纵向管理，使整个流程的顺畅运行
## 行动

- [x] 阅读《搞定Ⅰ》 
	- [[《搞定Ⅰ》阅读笔记]] 
	- 书中的流程在结合实际后，未能顺畅运行？
	- “自然计划法”应该何时运用？应该如何运用？
- [x] 阅读《搞定Ⅲ》
	- 每天无法长时间坚持阅读，一天的有效阅读时间不足1.5小时？
	- [[个人管理系统.xmind]]
- [x] 寻找合适的插件，用于日程表的查看、回顾
	- 插件市场暂无满足要求的插件？
- [x] 编写插件实现自定义功能
	- 无JS功底？
- [x] 使用dataview代码实现日程表中任务查询
	- [[Field 速记]]
	- `.map(group =>`${group.key} - ${group.rows.map(p => p.text).join().split("⏳")[0]}`)` 使用.map()将时间点与任务文本合并至同一行，返回结果就不再具有task的属性，因此不能放在dv.taskList()中，否则会执行失败
	- 在日程表中安排计划时，可以利用`task插件`实现快速插入emoji和日期选择器，以方便快速计划安排
- [x] 利用坚果云将obsidian部署到手机端
	- 部署方式复杂且不实用？
- [x] 使用第三方插件将flomo中的内容自动同步至本地
	- 需要开通会员获取APIKEY，且需要熟练掌握JavaScript编程？
- [x] 优化看板dataviewjs代码，显示当天可执行的行动 ⏳ 2024-08-27
- [x] 优化看板dataviewjs代码，覆盖重复性类型的任务的查看、回顾
- [x] 优化看板dataviewjs代码，显示有子任务的任务时，查询结构仅显示主任务
- [x] 优化看板dataviewjs代码，使带有时间的预约按照时间顺序排序
- [x] 优化看板dataviewjs代码，使查询结果中隐藏不需要的描述
- [x] 二次阅读《搞定Ⅲ》，梳理GTD流程
- [x] 梳理GTD执行流程，记录相关问题 ⏳ 2024-09-02
- [x] 梳理「搭建“自我管理”系统」的“行动”结构 ⏳ 2024-09-05
	- 为项目添加fronmatter，显示推进速度（推进频率）
- [x] 优化dataviewjs代码，显示日程表中的提醒信息 ⏳ 2024-09-05
	- 将“寻找整改类项目复盘方法”作为提醒信息放在日程表中，并链接到对应的项目
- [x] 编写CSS代码将dataviewjs查询的任务结果的项目符号更改为类似列表的项目符号 
	- 将看板中任务的复选框更改为列表符号后，感觉不能清晰的反应任务的完成情况？
- 将GTD回顾过程转化为mermaid，放置在“1-Idea”文件顶部，方便回顾时提醒
- 在mermaid中添加链接，方便快速定位对应清单，减少寻找切换清单的时间
- 优化看板dataviewjs代码，显示指定日期的预约（列表，非任务类，无需使用复选框表明状态）
- 利用🛫属性，编写dataviewjs查询“当天需要或想看到的信息”，并显示在看板中（与预约、已确定日期的活动分开）
- 设置每个项目的frontmatter的截止时间，在“日程表”和“尽快行动”对项目中的每个行动进行排序
- 优化dataviewjs代码，使查询结果可以显示一个时间段的行动
## 闪念

- 使用看板回顾日程表时，不能清晰了解未来某天或某几天的活动？
- 对于“行动清单”中的行动不确定放在「“尽快行动”清单」还是「日程表」中？
- 执行行动时得到灵感，临时想要执行一个未创建的行动，要怎么办？对应的处理流程是什么？
- 日程表中的「预约」、「信息」还未找到合适的使用方法和场景？
- 解决dashboard中行动的选择与排序问题
- 思考行动（含项目）下📝和🧷的存放位置的解决方案
	- 如果项目的行动下的想法或灵感内容较多，可以单独创建一个笔记存放
- 结合现实的任务梳理系统运行流程
- 项目中的行动并非一定有明确的时间计划，可能此项目很重要但是并非很紧急，显示的“尽快行动”中也许是一个好的方式？
- “尽快行动”清单中的行动在将来也许会组成一个更庞大的项目
- 将某些与项目有关的行动从”将来也许“清单移动至项目中，或许可以减少寻找和思考的时间？
- 若无法判断某一个任务是「行动」还是「项目」，应该如何处理？
- “尽快行动”清单中的场景标签未设计完成，未发挥清单的作用？
- ”决定’学习CAD‘的下一步行动“本身并不是一个行动「如果你对自己说：“噢，我的下一步行动就是‘决定这件事应该如何处理’”，那又该怎么办？要知道“决定”并不是一个真正的“行动”，因为真正的“行动”需要耗费时间去执行，而“决定”并非如此」，那它应该被放在什么位置才能起到提醒的作用，因为如果没有提醒，就很容易忘记，项目进度也会被延后？
- 类似“洗衣服”、“给自行车打气”这样的生活小事，记录在本系统显得有些不合适。因为它太小太简单，以至于记录它花费的时间都能完成这件事，但是不记录，脑子里又总是想着它们？
- 有两个相关依赖或关联项目，若其中一个项目的一个行动需要在另一个项目的某个行动完成后才能执行时，可以适当考虑将其中一个项目的后续行动提前来提升效率
- 当执行一个行动时，它对应着2个或以上可能执行的“下一步”（例如：骑自行车时链条总是卡壳？对应的下一步可能是“在地图上寻找合适的修理地点”，同时完成此步后，还需有接下来的“下一步”；也可能是“在网上搜索类似的解决方案”），应该如何记录提示信息？记录在哪里？原来的问题应该如何处理？
- 尽快行动清单，需要如何串联起整个GTD流程？
- 若某个行动花费的时间超出计划时间，或在计划时间内未完成当前的行动，下一步应该如何安排？
## 资料
```dataviewjs
	const links = dv.current().file.outlinks
	const unlinks = links.values.filter((values, index, self) => {
	  return self.findIndex((t) => t.path === values.path) === index
	})
	links.values = unlinks
	dv.list(links)
```
