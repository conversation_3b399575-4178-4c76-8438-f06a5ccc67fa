- 使用Clickup的初衷是能够更加快速，便捷的跟踪日常的工作。而质量的基本工作是问题及异常的跟踪及闭环。
- 其他人的工作过来询问我如何做，但是这个工作又不是我负责的，我该如何处理
- 拟定工作行动计划时，有时会碍于领导的面子或当时情景的压力，主动将任务提前，实际执行任务时却没有同步调整（大多因工作任务繁重，其实根本忙不过来），导致任务未能如期完成，还老是被领导催作业
- flomo记录的灵感，如何进行排序和选择并确认主题。或者挑选对目前最重要的事情
- 早上起来上班第一件徘徊在脑海的事情居然是今天要做什么？不知道质量要怎么管理
- 总是感觉自己的工作有很多要做，但是实际花时间来做时发现，不知道要做什么
- 好了几个月的质量，发现自己根本不知道质量要做什么？自己的主要工作内容也不能很熟悉的说出来
- 下发的任务仓库不能在时效内保质保量的完成。导致很工作延迟
- 对于下属没有完成的任务或者没有达成效果的任务需要进行补充跟进处理
- 固有的思维模式让我的工作效率变得更低。处理事情总是在人情、制度等不同的情景下左右徘徊无法取舍。我找到了更高效的思维方式，处理事情最简单的方法还是返璞归真，直接面对问题采取最直接的方式，同时，在这个条件下再去考虑其他因素。--海宁仓LOST库存分析，找网易客户取入库数据
- 滴答清单收集箱中的任务太多，让我不知所措，不知道哪些是对我来讲最重要的，或者说是判断今天需要做什么？感觉把自己要做的事情收集之后，心中的压力变小了，后面就有理由拖一拖了。
- 滴答清单中子任务无法关联主任务，让人很是头疼，强迫症甚至让我无法好好安排工作清单。
- 对于滴答清单一直有一个困惑，理论上说“工欲善其事必先利其器”，但是分解工作总是担心时间用的太多，每次都是分解不到位，导致计划时间点的工作无法完成。很多事情自然停留在想法层面，或者急余分解，内容分解不细致。总的来说，自己的工作没有复盘回顾总结，没有积累沉淀，处理事情就会很吃力。现有的工作内容，还是需要区分复杂程度、场景，对于程序繁琐的工作分解的时间可能会在很长一段时间内占据工作时间的大部分。对于此项分解，需要考虑具体的时段（工作8小时的某个时间点、或者某个非工作时间点——且必须是思路清晰，精神状态较好的情况）
- 滴答清单梳理任务后，感觉任务太多没有办法计划时间

- 工作中经常会因为对某一个知识点感兴趣，转而不自觉全身心投入去做它，而忘记了当前最重要的事情，打乱了自己的计划

- 核心任务中出现的问题时，除非影响到当前任务目标的达成，否则仅需要在定期追踪项目或当前任务出现新的目标时再重新思考这些问题是否需要重新安排；因此当前核心任务的完成状态取决于任务当前的目标是否达成
- 优化在安排周行动时的嵌入动作
- “防弹笔记”实用手札里面的内容，应该尽量保证为最新的实践结论、情景总结（错误的删除掉），同时应该禁止放入未解决的疑问
- 在执行行动时，总是有一种莫名的冲动，想要立即停下手上的工作，去执行自己当前很感兴趣的行动？
	- 安排行动时，针对这种行动可以提前安排，避免影响心情，降低工作积极性
- 6月1日，第一次没有完成当日的行动安排？
- 需要重新梳理“仓储经验总结”项目内容，将附件排程到对应位置
- 核心任务笔记行动下的思考、问题混在一起，如果能想到一个方法区分这些内容，会更加直观
	- 使用CSS以不同字体颜色显示？
- 以“仓库经验总结”为实例，修改项目结构
- 解决行动下不同思考、灵感、问题显示不直观的问题，方便行动安排和后期回顾
- 解构“健身减肥”项目
- 生活的意义是不是能够按计划完成设定的生活目标？
- 记录解决创建任务、项目文件时，文件重复的问题的相关思考
- 搜狗输入法在打字编辑时不显示状态栏
	- 设置-->常规-->全屏隐藏状态栏，取消勾选即可
- 核心任务笔记行动下的“代办”本质上和其他的思考、问题、灵感时一样的
- 使用HTML语法编辑绘制表格，可以让其具有更高的可定义性
	- 是否有插件可以实现在正文直接编写HTML代码
- 为每项工作添加专业知识、专业技能内容


- 目标回顾：彻底解决春潮托管仓客诉反复发生的质量问题。
- 评估结果：
	- 从业务长远达成角度切换至业务区/监管仓揽收可执行性不高，放弃切换考虑；盟仓考核协议补充协议确认签订
	- 春潮托管仓客诉问题的核心在于战区业务考核和质量考核之间的平衡，运营端的关注点与业务端的关注点存在冲突，运营端无法独干涉业务的取舍。解决问题的关键在于运营端将能做的全部做完的前提下，问题能自然的转移至业务端承接。
- 事件描述：
	- 春潮托管仓质量问题反复发生，与监管仓沟通前往现场督导，但是监管人员监督效果较差，无人员在场质量问题会出现反弹。在考虑现有业务的前提下，计划将业务切换之监管仓或业务区，但会出现客户流失的风险。此业务揽收作业的甲乙方均为客户自己，客户的目标是通过揽收降低现有仓库运营成本的投入
- 原因分析
	- 运营的问题多与业务问题同时出现，未综合考虑仓库整体的情况，无法从单一运营端解决问题
	- 自我定位不清晰。作为运营专员，首要的职责是分摊运营负责人的考核，需要时常站在运营负责人的角度分析问题，不能让战区负责人对整个部门有评价，需要考虑整个运营部门的荣辱得失

- 事件描述：2022年5月27日，计划与温州滨海仓讨论关于库存差异分析及理赔流程提交的工作。在与仓库反复沟通调整流程提交注意事项与问题后，在当天的17:00前确定了初步结论——因时间久远，但是初步确认了疑似产生的原因。因理赔相关问题已搁置很久，决定不再深究。责任直接判责为原仓库负责人、主管。在输出结果应用的相关邮件后，可以直接提交理赔流程。
	- 我自己根据事件草拟了一封邮件，因考虑行政处罚，与人资同事沟通希望可以帮忙审批，尽快公邮发出，方便仓库尽快提交流程。但，人资以邮件为能写清楚事件、未明确后续的方向为由驳回，也没有说明具体审批不通过的原因，在我态度强硬后，人资直接质疑我的工作效率
	- 行政处罚是人资的工作，不能因为事件搁置较久，就越界干不属于自己的工作；

- 工作没有沉淀已经严重影响到了日常的工作，目前复盘技能的提升已变成首要任务