## 流程

- 发布季度盘点[^1]通知
- 整理盘点计划（时间安排、盘点方案）
- 跟踪盘点进度
- 现场监盘
- 库存差异调整
- 盘点差异原因分析、改善
## 问题

- 盘点时长一般持续1周到半月不等，仓库大多数都是不停产盘点；未关注盘点效率（盘点成本）问题？
- 提报的盘点方案中的内容基本是无用的信息，区域应付总部，仓库应付区域；现场都有另一套实用的方案（先哪个区域再盘哪个区域？盘点要经过几轮盘点？如何安排每轮盘点的人员？初盘无异常的部分怎么验证？盘点中多货、少货如何处理？空库位什么时候盘点？高位货架、低位货架的盘点如何安排可以提升效率？差异盘点如何安排？等等……），但都基本没有形成文字？
- 仓库习惯了纸质盘点（不会影响库存准确率，且当前系统无法覆盖一轮、二轮盘点的场景，保证库存准确率和库位准确率数据的准确性），因基本都是不停产盘点，时间跨度较长，线上盘点数据合并、库存差异结果计算困难（商品存在一品多位情况，当前数据差异并非真实差异，有很大可能是库存漂移），基本都不愿意进行RF盘点？
- 部分仓库因为促销、客户盘点等原因，经常会延期，一般都是运营负责人审批同意的，出于成本考虑，基本都很支持，至于盘点进度延后出现的生产问题、差异积累问题好像都不是事情。经常会感觉领导好像不关心库存差异，又好像挺关心的？
- 了解区域整体盘点进度情况，包括盘点差异，可能是为了有“邮件输出”，让领导知道质量组在干活；也有可能是了解整体进展，推动工作在正常时间内完成？
- 即使发现了库存差异，也都是“小打小闹”或方便跟进仓库差异处理工作的完整进度，对库存控制没有什么帮助？更何况仓库提报数据基本都是处理过后的，库存准确率都是100%？
- 理论上库存盘点前需要经过客户、仓库双方系统对账环节，一个季度1次的频率其实也符合逻辑，但是后期总部将系统对账工作单独拎出来，要求每月1次，同时要客户参与并邮件回复、没有回应的提供与客户沟通截图等等一系列复杂的流程，完全是本末倒置，彰显业绩？
- 初衷是为了让仓库认真盘点，给仓库一个警醒或震慑，但从双捷成立后，非常注重成本控制，经常禁止出差；后因质量模块一直不肯加人手，就不了了之了？
- 仓库提报数据因基本都是提前处理过才上报的，所以基本没有库存差异原因分析；即使有部分客户有个别小差异，也都分析不出来具体原因（日常盘点基本停止了，也没有分析的积累），也就无处改善了？
- 对于特大库存差异，基本都是存在重大运营事故，差异瞒不住了，且大头的差异都是事故造成了，所以这部分的差异产生原因分析、改善措施，其实都是运营事故的原因分析，在分析时思考方向偏向库存差异而已？
- 一般库存差异基本都是仓库主管与客户私下沟通，通过系统推送自提出库单、虚拟入库单直接调整的，很少进过区域？
- 对接系统的客户需要区域层层审核，并由第三方（订单中心）推送库存调整出入库单；未对接的需要对调整过程保留与客户的沟通邮件，以上方式让库存差异调整变得更没有意义，大家都不会再上报异常（我记得当时邮件处理一个客户的库存差异调整，层层的邮件审核还没有结束，这件事就不了了之了）？

[^1]: [[《仓储配送仓库盘点管理制度》.doc]]