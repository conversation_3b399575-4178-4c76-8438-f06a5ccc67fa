/**
 * 全局自动技术债工作流脚本
 * 功能：
 * 1. 通过Templater Startup Templates自动初始化
 * 2. 监听所有技术债文件的修改
 * 3. 自动检测表格中状态为"进行中"的行动
 * 4. 自动在Plan文件中创建对应任务
 */

module.exports = async (tp) => {
  // 避免重复初始化
  if (window.globalTechDebtWorkflowInitialized) {
    console.log("全局技术债工作流已经运行中");
    return;
  }

  console.log("初始化全局技术债工作流...");

  const vault = app.vault;

  // 创建全局监听器
  const globalListener = async (file) => {
    // 只处理技术债文件
    if (
      file.path.includes("技术债") &&
      file.path.match(/td-\d{8}-\d{2}\.md$/)
    ) {
      console.log("检测到技术债文件修改:", file.path);

      // 延迟执行，确保文件保存完成
      setTimeout(async () => {
        await processTechDebtFile(file);
      }, 1000);
    }
  };

  // 注册全局监听器
  vault.on("modify", globalListener);

  // 保存监听器引用
  window.globalTechDebtListener = globalListener;
  window.globalTechDebtWorkflowInitialized = true;
  console.log("全局技术债工作流已启用");
};

// 处理技术债文件
async function processTechDebtFile(techDebtFile) {
  try {
    console.log("开始处理技术债文件:", techDebtFile.path);

    // 提取项目名称
    const pathParts = techDebtFile.path.split("/");
    let projectName = null;
    for (let i = 0; i < pathParts.length; i++) {
      if (pathParts[i] === "3-过程资产" && i + 1 < pathParts.length) {
        projectName = pathParts[i + 1];
        break;
      }
    }

    if (!projectName) {
      console.log("无法提取项目名称");
      return;
    }

    // 读取技术债文件内容
    const techDebtContent = await app.vault.read(techDebtFile);

    // 解析偿还计划表格
    const tableRegex =
      /# 2\. 偿还计划[\s\S]*?\n(\|[^\n]*\|\s*\n\|[^\n]*\|\s*\n(?:\|[^\n]*\|\s*\n)*)/;
    const tableMatch = techDebtContent.match(tableRegex);

    if (!tableMatch) {
      console.log("未找到偿还计划表格");
      return;
    }

    const tableContent = tableMatch[1];

    // 解析表格行
    const rows = tableContent
      .split("\n")
      .filter((row) => row.trim() && !row.includes("---"));
    const headerRow = rows[0];
    const dataRows = rows.slice(1);

    // 解析表头
    const headers = headerRow.split("|").map((h) => h.trim());
    while (headers.length > 0 && headers[0] === "") headers.shift();
    while (headers.length > 0 && headers[headers.length - 1] === "")
      headers.pop();

    const actionIndex = headers.findIndex((h) => h.includes("行动"));
    const statusIndex = headers.findIndex((h) => h.includes("状态"));

    if (actionIndex === -1 || statusIndex === -1) {
      console.log("表格格式不正确，未找到'行动'或'状态'列");
      return;
    }

    // 查找状态为"进行中"的行
    const inProgressActions = [];
    for (const row of dataRows) {
      if (!row.trim()) continue;

      const cells = row.split("|").map((c) => c.trim());
      while (cells.length > 0 && cells[0] === "") cells.shift();
      while (cells.length > 0 && cells[cells.length - 1] === "") cells.pop();

      if (cells.length > Math.max(actionIndex, statusIndex)) {
        const action = cells[actionIndex] || "";
        const status = cells[statusIndex] || "";

        if (status === "进行中" && action.trim()) {
          inProgressActions.push(action.trim());
        }
      }
    }

    if (inProgressActions.length === 0) {
      console.log("未找到状态为'进行中'的行动项");
      return;
    }

    console.log("找到进行中的行动:", inProgressActions);

    // 查找对应的Plan文件
    const planFileName = findCurrentWeekPlanFile(projectName);
    if (!planFileName) {
      console.log("未找到当前周的Plan文件");
      return;
    }

    const planFile = app.vault.getAbstractFileByPath(planFileName);
    if (!planFile) {
      console.log(`Plan文件不存在: ${planFileName}`);
      return;
    }

    // 检查并创建任务
    const planContent = await app.vault.read(planFile);
    let updatedPlanContent = planContent;
    let newTasksCount = 0;

    for (const action of inProgressActions) {
      const taskText = `- [ ] ⟦技术债⟧${action}`;

      if (!planContent.includes(taskText)) {
        updatedPlanContent += "\n" + taskText;
        newTasksCount++;
        console.log("自动添加任务:", taskText);
      }
    }

    // 更新Plan文件
    if (newTasksCount > 0) {
      await app.vault.modify(planFile, updatedPlanContent);
      // 显示通知（可选，避免过于频繁的通知）
      if (newTasksCount <= 3) {
        new Notice(`自动创建了 ${newTasksCount} 个技术债任务`);
      }
    }
  } catch (error) {
    console.error("处理技术债文件出错:", error);
  }
}

// 辅助函数：查找当前周的Plan文件
function findCurrentWeekPlanFile(projectName) {
  try {
    const today = new Date();
    const year = today.getFullYear();

    // 计算当前是第几周
    const startOfYear = new Date(year, 0, 1);
    const pastDaysOfYear = (today - startOfYear) / 86400000;
    const weekNumber = Math.ceil(
      (pastDaysOfYear + startOfYear.getDay() + 1) / 7
    );

    const planFileName = `2-项目/${projectName}/1-每周规划/Plan-${year}-WK${weekNumber
      .toString()
      .padStart(2, "0")}.md`;

    console.log("查找Plan文件:", planFileName);
    return planFileName;
  } catch (error) {
    console.error("查找Plan文件出错:", error);
    return null;
  }
}

// 停止全局工作流的函数（调试用）
window.stopGlobalTechDebtWorkflow = function () {
  if (window.globalTechDebtListener) {
    app.vault.off("modify", window.globalTechDebtListener);
    window.globalTechDebtListener = null;
    window.globalTechDebtWorkflowInitialized = false;
    console.log("全局技术债工作流已停止");
    new Notice("全局技术债工作流已停止");
  }
};
