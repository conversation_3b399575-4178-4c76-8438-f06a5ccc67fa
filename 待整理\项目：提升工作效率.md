---
Scene: 工作
Progress: 计划酝酿
startDate: 2024-05-26
---
## 计划

- 动机
	- 晚上总是不能按照计划的时间上床休息，而且拖延到很晚，导致第二天早上不能按时起床、精神不佳、白天工作效率下降
	- 白天执行同类型或相似的工作任务太多时，总会觉得好像没有什么成就感（即使很好的完成并都有成果产出），“下班后”的时间就想拼命的玩
	- 早上起床后若没有按照原计划做事，就会一边纠结如何回到正轨，一边去做其他无关紧要的事情（做之前一直想要做却觉得有些耽误时间的事情，比如：研究obsidian的某个有趣的插件）
	- 中午午休30分钟通常不够，一般至少1小时起步；感觉午休时间太长，会影响晚上休息，同时工作后也许没有这么长的时间休息，养成习惯后不好改掉
- 目的
	- 白天工作学习期间可以拥有良好的精神，以保证工作效率
	- 养成良好的工作习惯，以保证获得足够的成就感
- 目标
	- 制定一个科学的作息时间表，并严格执行
	- 掌握提升工作效率的技巧、方法
- 任务
	- [[任务：制定作息时间表]] 
	- 查找提升工作效率相关书籍、资料（[[任务：查找提升工作效率相关书籍、资料]]）

## 输入
```dataviewjs
const project = dv.current().file.name;
dv.list(
	dv.pages('"3-输入"')
	.where(p => p.project?.includes(project))
	.sort(p => p.file.mtime,'desc')
	.file.link
)
```
## 输出
```dataviewjs
const project = dv.current().file.name;
dv.list(
	dv.pages('"4-输出"')
	.where(p => p.project?.includes(project))
	.sort(p => p.file.mtime,'desc')
	.file.link
)
```