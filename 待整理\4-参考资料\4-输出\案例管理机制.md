---
Project:
  - 项目：仓储工作经验总结
---
- 运行结构图
```mermaid
	flowchart LR
	c((记录))
	a((分享))
	b((检查))
	a-->b-->c-->a
```

- 功能（场景）
	- 日常运营：侧重日常管理的琐碎事务，目的在于平衡、稳定日常运营质量，保持一个动态平衡，避免质量恶化
	- 大促高峰：侧重大促前的筹备，目的在于通过提醒、补充大促筹备期间的准备事项，减少高峰期间的运营事故的发生

- 环节说明
	- 分享：利用会议、培训机会，有针对性的匹配当前运营阶段、环节、事件等，宣导分享相关案例，给管理者一定的提醒、启发；分享期间，需要分析当时每一个步骤、行动，反思具体的可借鉴、可优化、可避免的做法
	- 检查：结合当前情况，引导仓库对当前的问题、风险进行优化，并检查其执行结果
	- 记录：从案例的角度，结合事件的特点（可编写成为案例），记录事件的每一步具体行动、管理者的思考等
- 注意
	- 依据运行结构图，「分享」、「检查」、「记录」是一个有机循环，其中任何一个环节断裂，机制将会失效