---
areas:
  - 项目管理
created_Date: 2025-06-04
update_Date: 2025-06-04
---
**场景描述**

- 事件/契机：根据GTD的管理思想，将个人生活学习中无法一步完成的任务以项目的方式进行管理，但项目的不确定性较大且敏捷的“用户故事”的管理方式不适用于当前情况
- 特征：任务复杂庞大、不确定性较大

**目标**

- 将看宏大、模糊或复杂的目标转化为可执行、可追踪、责任明确的关键结果和行动，以确保目标最终能够被有效达成。

**操作步骤**

1.  明确核心目标：使用"5why"法寻找想要做的某件事的背后的根本原因，即根本目的
2. 创建目标：基于第一步明确的核心意图，提出一个鼓舞人心、定性（方向性）且有时间限制的目标陈述
3. 制定KR：为目标（O）设定2-5个可量化或可衡量、可验证的具体结果指标
	1. 若KR本身不可量化，需要采用高度具体的、可验证的里程碑（Milestones）或明确的“是/否”标准作为替代方案
4. 设定周期目标：为一个或多个KR设定1-3个周期性的量化的或可衡量的目标
	1. 若周期性目标本身不可量化，以明确交付物/行动证据代替
	2. 若可量化，需定义基准值和目标值
5. 设定验收标准：为周期目标设定对应的验收标准
	1. 对于可量化的目标，以“完成[具体动作]，使[指标]从[当前值]达到[目标值]（数据来源：[xxx系统/报告]）”描述
	2. 对于不可量化目标，以“交付[具体成果物]，并通过[验证方式]/完成[关键行动]，产生[可追溯证据]”描述
	3. 对于复杂目标，拆解为多项必须同时满足的条件（清单）
6. 执行与持续跟踪：将周期目标分解成具体的、可执行的任务或计划，并建立定期的检查点（KR进度审查、障碍识别、快速调整）
7. 复盘与评估：周期结束后进行OKR复盘，评估KR进展情况及目标达成情况、优化设定下一周期目标、总结经验教训

**检查标准**

- O是否为核心意图
- KR的数量在3-5个之间，KR的描述是否已量化或高度具体或有明确的“是/否”标准
- 周期目标的描述是否量化或明确交付物/关键行动
- 验收标准是否符合3条公式
- 是否定期进行KR进度审查、障碍识别、快速调整
- 是否进行周期性复盘