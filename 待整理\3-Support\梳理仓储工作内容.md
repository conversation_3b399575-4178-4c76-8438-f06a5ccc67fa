---
endDate: 2024-09-10
repeat: everyDay
---
## 概述

- 动机：了解过去工作经历、内容
- 目标：掌握过去仓储工作中常见事务的工作流程、使用的专业知识；了解日常工作的常见问题；
- 愿景：在未来的某一天某些内容可以发挥一些价值
## 行动

- [x] 回忆、梳理仓储岗位经历
	- [[仓储岗位经历#岗位经历|岗位经历]]
- [x] 回忆、列举常见工作内容清单
	- [[仓储岗位经历#主要内容|主要内容]]
- [x] 梳理“监控KA客户KPI达成”相关内容
	- [[KA客户KPI监控流程]]
- [x] 处理VIKA图床上传后，无法找到文件源问题
	- picgo客户端“相册”中的图片为缓存文件，目的是为了更好的管理上传至vika图床中的源文件的属性（例如：文件名），与源文件不是同一文件，删除后不会影响源文件
	- picgo除了可以上传图片外，还可以上传word、Excel等文件
	- “相册”有图片，但是vika维格表中无源文件，很可能是客户端设置的“字段名称”与维格表中存放图片的字段不一致
- [x] 梳理“监控KPI指标达成”相关内容 ⏳ 2024-08-31
	- [[监控KPI指标达成]]
- [x] 梳理“假期人效目标监控”相关内容 ⏳ 2024-08-31
	- [[假期人效目标监控]]
- [x] 梳理“假期质量管理”相关内容 ⏳ 2024-09-01
	- [[假期质量管理]]
- [x] 梳理“薪酬计件改革”相关内容⏳ 2024-09-01
	- [[”薪酬计件“改革]]
- [x] 梳理“季度盘点”相关内容 ⏳ 2024-09-02
	- [[季度盘点]]
- [x] 梳理“NPS客户满意度”相关内容 ⏳ 2024-09-02
	- [[「NPS」客户满意度]]
- [x] 梳理“太湖-撤并仓项目”相关内容 ⏳ 2024-09-03
	- [[“太湖”撤并仓项目]]
- [x] 梳理“松下-运营整改项目”相关内容 ⏳ 2024-09-03
	- [[“松下”运营整改项目]]
- [x] [课时1：学习“项目复盘基本功最佳实践”](https://www.bilibili.com/video/BV1BY411D7AN/?spm_id_from=333.337.search-card.all.click&vd_source=6ed15181e2c4289ca2ab39f164282627) ⏳ 2024-09-05
- [x] [课时2：学习“项目复盘基本功最佳实践”](https://www.bilibili.com/video/BV1BY411D7AN/?spm_id_from=333.337.search-card.all.click&vd_source=6ed15181e2c4289ca2ab39f164282627) ⏳ 2024-09-07
- [x] [课时1：顶级复盘法](https://www.bilibili.com/video/BV11dWUeAEuo?p=8) ⏳ 2024-09-09
- [x] 课时1：《复盘思维：用经验提升能力的有效方法》 ⏳ 2024-09-11
- [x] 课时2：《复盘思维：用经验提升能力的有效方法》 ⏳ 2024-09-14
- 课时3：《复盘思维：用经验提升能力的有效方法》
- 梳理“金华领跑仓运营整改”相关内容
- 梳理“上海洞泾仓破屏运营整改”相关内容
- 梳理“上海仓客户投诉整改“相关内容
- 梳理“杭州钱江仓运营整改”相关内容
- 梳理“嘉兴海宁仓（网易）运营整改”相关内容
- 梳理：杭州鸿兴仓运作质量稽核“相关内容
- 梳理“二线员工星级认证”相关内容
	- [[二线员工“星级认证”]]
- 梳理“客诉管理”相关内容
	- [[客诉管理]]
- 梳理“疏漏发-专项”相关内容
	- [[“错漏发”专项]]
- 梳理“大促-岗位培训”相关内容
	- [[大促-岗位培训]]
- 梳理“大促-权限管理”相关内容
	- [[大促-WMS权限管理]]
- 梳理“大促-巡查”相关内容
	- [[大促-巡查]]
- 梳理“大促-案例分享与记录“相关内容
	- [[大促-案例分享与记录]]
- 梳理“商品信息管理“相关内容
	- [[大促-商品信息管理]]
- 梳理“运作稽核管理“相关内容
	- [[运作稽核管理]]
## 闪念

- 回顾运营整改项目时，感觉没有总结出有价值的东西？
## 资料
```dataviewjs
	const links = dv.current().file.outlinks
	const unlinks = links.values.filter((values, index, self) => {
	  return self.findIndex((t) => t.path === values.path) === index
	})
	links.values = unlinks
	dv.list(links)
```