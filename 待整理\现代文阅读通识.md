---
number headings: first-level 1, start-at 1,max 6,1.1, auto
---
> [!question] 线索区（关键词/问题）

```mermaid
mindmap
	root(说明文)
		特征及对策
		基本思想
		理解技巧
		阅读原则
		结构化分析
		顺序及作用
		分类
			按对象
			按语言
		方法
			举例分类作比较
			定义诠释画图表
			数字比方摹状貌
			引用说明引资料
```

- 议论文
```mermaid
	mindmap
		root(议论文)
			特征及对策
				基本思想
				理解技巧
				阅读原则
				结构化分析
			论点
				定义
				分类
			论据
				定义
				分类
			论证
				方式
				方法
				作用
				结构
				过程/思路
			语言特点
```
- 中心句
```mermaid
	mindmap
		root(中心句)
			定义
			位置分类
				显性
				隐性
			其他重要句子
				总起句
				总结句
				支撑句
			文体差异（说明、议论）
			与主旨的关系
```
> [!abstract] 笔记区
# 1 说明文

- 定义：介绍或解释对象的文体
- 基本思想：文字 + 结构 → 对象
- 阅读原则
	- 精细化阅读
	- 高度概括化
	- 语境化理解
		- 字词短语不离句
		- 句不离段
		- 段不离篇
- 结构化分析
	- 结构层次性强
	- 具体方法
		- 抓标题
		- 抓第一段，最后一段一级第二段第一句
		- 每一段开头和结尾
- 顺序：时间、空间、逻辑（作用：条理性、便于读者理解）
- 逻辑顺序
	- 因果逻辑
		- 原因 → 结果（适用于结果明确时）
		- 结果 → 原因（多用于问题分析）
	- 事理演进顺序
		- 一般 → 特殊
		- 现象 → 本质
		- 表象 → 原理
		- 具体 → 抽象
	- 分类比较结构：确定分类标准→建立比较维度→系统化呈现
		- 横向比较（同类事物差异）
		- 纵向比较（自身发展变化）
	- 问题解决模型：问题界定→原因分析→解决方案→效果预测
	- 功能系统解析：核心功能→支持系统→交互关系
- 分类：
	- 按对象
		- 事物、事理
	- 按语言
		- 平实、生动
- 说明方法：举例分类作比较、定义诠释画图表、数字比方摹状貌、引用说明引资料
- 不同方法的作用
	- 举例子 → 具体、说服力
	- 分类别 → 更具条理性
	- 列数字 → 准确、客观、更具说服力
		- 句子仅一个数字时，优先考虑其他
	- 打比方 → 生动、形象
	- 作引用 → 更具文采、具体、说服力
	- 作比较 → 突出、强调
	- 下定义 → 更全面、易理解
	- 作诠释 → 充分、具体、容易理解
	- 画图标 → 直接、清晰
	- 摹状貌 → 形象、具体
- 下定义 VS 作诠释
	- 下定义 → 概括
	- 作诠释 → 解释
- 语言特点：严密性、平实性、生动性（表达方式、修辞手法、句式富于变化）
- 标题作用
	- 点名对象
	- 引起读者兴趣
	- 运用修辞手法，揭示对象特征
	- 概括内容
	- 问句 → 引发思考
- 段落作用
	- 首段
		- 内容上 → 分析、概括
		- 结构上 → 引出对象、增加文学色彩、总领全文、引起下文
	- 中间段：结构上 → 承上启下（总领全文、引出下文）
	- 结尾
		- 内容上 → 再次强调
		- 结构上 → 总领全文、呼应形状（结构严谨）
# 2 议论文

- 基本思想：文字 +结构 → 内容 → 论点（观点）
- 理解技巧：以论点为中心
- 阅读原则
	- 精细化阅读
	- 高度概括化
	- 语境化理解
		- 字词短语不离句
		- 句不离段
		- 段不离篇
- 结构化分析
	- 结构层次性强
	- 具体方法
		- 抓标题
		- 抓第一段，最后一段一级第二段第一句
		- 每一段开头和结尾
- 议论文3要素：论点 + 论据 + 论证
- 论点：作者要证明/反驳的观点
- 论点 VS 论题
	- 论题 → 范围，论点 → 观点/看法/主张
	- 论题 → 短语，论点 → 完整的句子（肯定或者否定）
	- 论题包含论点
- 中心论点 VS 分论点
	- 中心论点 → 全文的灵魂，仅一个 ；分论点 → 可以有多个（中心论点再分）
	- 分论点为中心论点服务
- 概括论点的方法
	- 看标题，论题 OR 论点
	- 看开头，统领全文？
	- 看结尾，总结性句子？
	- 看中间，过渡句？
	- 表述不集中，自己概括
- 提出论点方式
	- 直接提出
	- 文末归纳
	- 引用材料
- 论据：用来证明论点的事实和道理
- 论据分类
	- 事实论据：具体事例（事实、统计数字、亲身经历……）
	- 道理论据：已经被长期实践检验的正确的观点。（著作和权威性言论、名人名言、自然科学……）
- 论证：用论据证明/反驳观点态度时采用的方法和过程
- 论证方式
	- 立论
	- 驳论
- 论证方法
	- 类比论证
		- 举例论证
		- 比喻论证
		- 道理论证
		- 对比论证
		- 正反论证
	- 演绎论证
		- 直言三段论：大前提（普遍原理）→ 小前提（具体对象）→ 结论
		- 假言推理（条件推理）
		- 选言推理（排除法）
		- 归谬法（反证法）
		- 数学证明（基于公理体系的严格推导）

| 方法   | 含义                                     | 作用      | 注意                                                       |
| ---- | -------------------------------------- | ------- | -------------------------------------------------------- |
| 类比论证 | A具有a、b、c、d的属性，B具有a、b、c的属性，所以，B可能具有d的属性 | 抽象概念具象化 | 1、适当性：不能保证结论的正确性，下结论不能过于绝对<br>2、相关性：类比的事物应该是同类的          |
| 举例论证 | 通过具体事实来证明观点                            | 具体、有说服力 | 1、切合性：举出的例子一定要符合论点，不可过度延伸<br>2、突出性：突出事例与论点之间的联系<br>3、新颖性 |
| 比喻论证 | 用形象的比喻者之理去论证抽象的被比喻者（论题）之理              | 生动、形象   | 1、相似性：本体与喻体的逻辑一致<br>2、形象性：让本体的道理更直观的呈现                   |
| 道理论证 | 通过引述一些名言、俗语来证明观点                       | 概括、深入   | 1、真实性<br>2、切合性，相关性要强                                     |
| 对比论证 | 从事物相反或相异的属性的比较中来揭示需要论证的论点的本质           | 突出、强调   | 1、相反行：双方相互对立，不能含混<br>2、突出性：要突出论点的一方                      |
| 正反论证 | 从正面和反面（归谬法）证明观点                        |         |                                                          |

- 论证过程/思路：论点怎样被提出？论点怎样被证明？联系全文的结构，是否有总结？
- 论证结构
	- 对比式：先正面，再反面
	- 纵式：逐层深入
	- 横式
		- 总论 → 分论 → 总论
		- 总论 → 分论
		- 分论 → 总论
- 语言特点：严密性、生动性、平实性、逻辑性（基于结构特征）
# 3 中心句

- 定义：在段落中处于主导地位，对其他句子起支配作用的陈述性语句
- 多元位置类型
	- 显性位置
		- 段首 → 总领下文，定基调（典型文体：说明文/议论文）
		- 段尾 → 总结升华，强化观点（典型文体：议论文/散文）
		- 段中 → 承上启下，突显转折（典型文体：议论文）
	- 隐性存在
		- 特征：需要读者自行提炼概括
		- 识别方法
			- 高频重复词追踪
			- 情感倾向分析
			- 支撑句反向推导
- 总起句 VS 总结句
	- 位置：总起句 → 段首，总结句 → 短尾
	- 功能：总起句 → 预设框架、引导下文，总结句 → 归纳提炼、深化认知
	- 句式：总起句 → 多使用判断句/设问句，总结句 → 常含"因此""总之"等标志词
- 中心句 VS 支撑句
	- 总分关系：中心句→细节展开（说明文常用）
	- 证明关系：中心句←论据支撑（议论文特征）
	- 递进关系：中心句→深层解析（论述文结构）
- 议论文 vs 说明文（文体差异）
	- 语言特征：议论文中心句 → 观点明确、含价值判断，说明文中心句 → 客观陈述、重事实描述
	- 结构功能：议论文中心句 → 分论点支撑总论点，说明文中心句 → 分类标准/说明对象特征
	- 位置规律：议论文中心句 → 多位于段首、偶用设问引出，说明文中心句 → 常置于段首，偶现段尾总结
	- 典型句式：议论文中心句 → "应当""必须""证明"等模态动词，说明文中心句 → "具有""分为""包括"等陈述动词
- 与主旨的关系辨析
	- 微观与宏观的互动
		- 中心句：段落层级的核心观点（局部焦点）
		- 主旨：全文层级的核心思想（全局统摄）
	- 关联模式
		- 直接呈现：主旨句=核心段中心句（常见于总分总结构）
		- 拼图合成：多个中心句共同指向主旨（如层层递进式论述）
		- 隐喻表达：中心句提供解读线索（文学性文本常见）
	- 分析工具：中心句关键词 + 作者态度词 + 文本高频词
# 4 序言

- 文体分类
	- 说明性文体：介绍书籍的创作背景、写作目的、内容框架或阅读方法
	- 议论性文体：学术著作或思想性较强的书籍中，作者或作序者可能提出观点或争议
	- 叙述性文体：自序或回忆录类书籍，强调个人经历或创作故事
	- 抒情性文体：文学类作品或纪念性文集，表达情感或价值观

# 5 分论点拆分策略

## 5.1 “应当”类立意

- 呼吁类型立意，形如“什么人（大多数情况是我们）应当做什么
- 基本逻辑 → 证明这件事是好的（中心论点）

（1）对谁有好处（对象角度）
分论点1：做某事对A有好处
分论点2：做某事对B有好处
并列关系

（2）有什么好处
分论点1：做A事情有好处1
分论点2：做A事情有好处2
分论点之间要有逻辑关联，不可跳跃

（3）应该做什么
分论点1：应当做A事的a方面
分论点2：应当做A事的b方面
部分 → 整体的逻辑拆分分论点，注意覆盖每个方面

（4）正方面论证
分论点1：如果做这件事，有什么好的影响？
分论点2：如果不做这件事，有什么坏的影响？
## 5.2 “原因”类立意

- 分析类立意，回答“为什么”的问题，形如“什么人做某件事的原因是什么？
- 基本逻辑 → 因果追溯，找到事情的原因（本质 = 做某事有什么好处）

（1）多个原因并列 → 分论点之间没有包含关系或因果关系
分论点1：做这件事情的原因有A
分论点2：做这件事情的原因有B

（2）“是什么”，“为什么”，“怎么办”
分论点1：这件事/现象是什么？
分论点2：这件事/现象的原因是什么？
分论点3：这件事/现象对我们有什么启示（我们应当……）？

## 5.3 “怎么”立意

- 重点在于“手段”、“方式”、“措施”，回答“怎么办”
- 基本逻辑 → 为了达成什么目的，什么人需要做什么事情

（1）根据不同的主体（不同的人需要做什么）→ 尽量把事情涉及的主体包含在内
分论点1：A需要做什么事
分论点2：B需要做什么事

（2）根据不同的途径
分论点1：要通过途径1来做
分论点2：要通过途径2来做

## 5.4 “1 + 1（+1）”类立意

立意可能不止一个重点或要论述的东西，常见辩证统一类立意


> [!info] 总结区





