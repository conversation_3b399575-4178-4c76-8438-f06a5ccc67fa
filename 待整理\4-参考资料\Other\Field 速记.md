## 语法

| Field name | Short hand syntax | 内联语法                                              | dataviewjs调用              |
| ---------- | ----------------- | ------------------------------------------------- | ------------------------- |
| due        | 🗓️YYYY-MM-DD     | （due::YYYY-MM-DD）或[due::YYYY-MM-DD]               | .where(t => t.due)        |
| completion | ✅YYYY-MM-DD       | （completion::YYYY-MM-DD）或[completion::YYYY-MM-DD] | .where(t => t.completion) |
| created    | ➕YYYY-MM-DD       | （created::YYYY-MM-DD）或[created::YYYY-MM-DD]       | .where(t => t.created)    |
| start      | 🛫YYYY-MM-DD      | （start::YYYY-MM-DD）或[start::YYYY-MM-DD]           | .where(t => t.start)      |
| scheduled  | ⏳YYYY-MM-DD       | （scheduled::YYYY-MM-DD）或[scheduled::YYYY-MM-DD]   | .where(t => t.scheduled)  |
- Tasks插件通过使用 Emoji来配置与任务相关的不同日期，从而引入了不同的表示法。在 Dataview 的上下文中，此表示法称为 `Field Shorthands`。Dataview 的当前版本仅支持如下所示的日期速记。不支持优先级和重复速记。
- 这些表情符号速记有两个细节。首先，它们省略了内联字段语法（无需 `[🗓️::YYYY-MM-DD]`）
- 一行任务或列表可以定义多个属性