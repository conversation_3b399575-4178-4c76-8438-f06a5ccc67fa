**语义化版本规范（Semantic Versioning，简称SemVer）** 是为软件版本号赋予明确含义的标准规则，旨在通过版本号的变化清晰传达代码变更的影响范围。以下是其核心规则：

---
### **1. 版本号格式**

版本号格式为：**`主版本号.次版本号.修订号`**（`MAJOR.MINOR.PATCH`），例如 `2.3.1`

- 主版本号（MAJOR）：  
    当发生 **不兼容的 API 变更** 时递增，且次版本号和修订号归零（如 `2.3.1 → 3.0.0`）
- 次版本号（MINOR）：  
    当新增 **向下兼容的功能** 时递增，修订号归零（如 `2.3.1 → 2.4.0`）
- 修订号（PATCH）：  
    当进行 向下兼容的问题修复 时递增（如 `2.3.1 → 2.3.2`）

---
### 2. 版本标识符

#### (1) 预发布版本（Pre-release）

- 通过 `-` 附加标识（如 `1.0.0-alpha.1`）
- 优先级：`alpha < beta < rc`（如 `1.0.0-beta.2 > 1.0.0-alpha.3`）

#### (2) 构建元数据（Build Metadata）

- 通过 `+` 附加标识（如 `1.0.0+build.20240315`）
- 不参与版本优先级比较（如 `1.0.0+build1 = 1.0.0+build2`）

---
### 3. 递增规则

|**变更类型**|**版本升级**|**示例**|
|---|---|---|
|修复向后兼容的缺陷|修订号（PATCH）↑|`1.2.3 → 1.2.4`|
|新增向后兼容的功能|次版本号（MINOR）↑|`1.2.3 → 1.3.0`|
|包含不兼容的变更（破坏性更新）|主版本号（MAJOR）↑|`1.2.3 → 2.0.0`|