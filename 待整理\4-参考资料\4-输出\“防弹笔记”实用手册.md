---
Project:
  - 项目：仓储工作经验总结
---
- **“清空大脑”，收集“噪声”** 
	- 原则：
		- 简单、直接，不会给大脑增加负担
		- 优先专注在当前行动上，出现的新思考、灵感、问题，不要急于排程
		- “噪声”需要经过排程才有价值，否则可以直接删除
	- 方法
		- 将“噪声”优先存放在「暂时笔记」中
		- 每周定期对「暂时笔记」中的内容进行排程
- **新建一则「核心任务笔记」**
	- 原则：
		- 
	- 方法：
		- 
	- 技巧：
		- 如何快速调整大纲文本行的位置？
			- 安装`outliner` 插件，使用`ctrl+shift+↑` 或`ctrl+shift+↓` 实现同层级大纲上下位置调整
		- 如何使用「」强调特殊字词
			- 在输入法中使用「自定义短语」功能可以提高输入效率
- **周行动安排**
	- 原则：
	- 方法步骤：
		- 先看时间提醒：有没有下周特定时间要开始的任务？（功能还未开发）
		- 「重点推进」
			- 1个个人目标任务或行动
			- 2个工作任务或行动
			- 3个时段的其他任务（早、中、晚3个时段，剩下其他要做的事情，都归类到其他琐事）
		- 看「暂时笔记」：有没有这周收集，还没排程（让任务、⾏动出现在该出现的位置）处理的任务？查看flomo看看还没有需要汇总、整理到OB暂存笔记的信息？
		- 看「计划酝酿」标签：有企划中项目可以推进下一步了吗？有没有需要立项的主题？
		- 看「定期追踪」标签：有没有需要回顾的任务？
		- 衡量剩余的空挡时间，根据已经排出的行动清单，对应任务或行动的时间情景
	- 注意：
		- 
- **行动执行**
	- 执行行动时，如遇到突然出现或联想的任务或行动，应该怎么处理？是打破原来的安排，重新计划？还是先排程，后续再安排
		- 先思考是否影响当日安排的后续任务或行动的执行；若影响进度推进、完成，需要将其添加到当日行动清单中；否则，将其排程至对应项目中即可
	- 执行行动时，如当前行动因为缺少某些条件，无法继续推进，后续行动应该如何调整？
		- 在次要事务类别中，增加对应条件的行动，以辅助暂停行动的继续推进
	- 周日安排行动时，内容尽量少一点琐事的事情，以便有更充裕的时间安排下一周的行动计划
	- 每日中午起床后20-30分钟，精神不好，需要安排一些尽量少动脑子的行动