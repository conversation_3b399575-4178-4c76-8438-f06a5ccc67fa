## 核心内容

- 横向管理[^9]
	- 准备：
		- 预留整段时间进行收集[^1]、处理[^2]
		- 保持一个安静且不被打扰的环境
		- 构建一个一般性的归档系统[^3]（归档分类过程不应该超过1分钟，否则，就说明你的归档系统还需要改进；注意区分专用归档系统[^4]）
			- 参考资料
			- 辅助信息
	- 收集：归拢材料
		- 第1步：收集周围环境中需要处理的事务（未尽之事[^6]）
		- 第2步：收集（捕获[^8]）存储在大脑中的事项
			- 琐碎的小事、重大事件、个人私事、工作问题等
			- 宁滥勿缺，千万别遗漏任何事情
			- “引子”清单
		- 注意：
			- 确保你已经彻底完成所有的收集工作后，再开始处理材料[^5]
			- 收集的内容过多，一个工作篮不够用-->堆在工作篮周围或立刻扔掉
			- 可能偏离到清理和整理的环节中去-->优先记录即可
			- 可能碰到一些以前收集和整理过的东西-->将这些内容当成需要处理的东西
			- 可能碰上一些紧急事项-->立即行动或建立“紧急事件”篮
	- 清理[^7]：清空收集箱
		- 原则
			- 首先处理工作篮中最上面的事务（从一端入手，按顺序处理；尽量减少优先处理最紧急、最有趣或者最吸引人的事情）
			- 一次一事
			- 永远不要把事务再次放回工作篮
		- 第0步：建立起相关目录
			- “悬而未决”信息提示
			- “等待”清单
			- 日程表或备忘录[^15]
			- “将来/也许”清单
		- 第1步：判断材料是否是可执行行动
			- 无法付诸行动
				- 垃圾
					- 视情况删除或保留
				- 待酝酿的事务
					- 记入“将来/也许”清单
					- 将提示内容写入日程表
				- 参考资料
			- 可付诸行动
				- 明确“下一步行动”[^11]
					- 即使对于一个非常简单的任务，当你试图找到“下一步行动”的时候，常常会发现你还需要进一步观察形势、考虑详细的行动步骤
					- 要知道“决定”并不是一个真正的“行动”，因为真正的“行动”需要耗费时间去执行，而“决定”并非如此
		- 第2步：处理“下一步行动”
			- 立刻执行（两分钟内可以完成）
				- 即使你面对的并不是什么重要任务，如果你能在两分钟之内完成，那你就应该立即将其完成
				- 就算你在完成了一个行动之后，没有达成任务目标，你也会更清楚接下来的“下一步行动”，而且，对于这个“下一步行动”，你还需要继续采用相同的标准来判断其执行方式。
				- 你不应该沦为每天都要执行两分钟行动的奴隶。这个原则应该主要用于对新内容进行处理
			- 委派他人（如果你自己不是完成任务的最佳人选）
				- 给对方发一封电子邮件
				- 写一张字条，然后交给某人
				- 给对方发条短信或语音留言
				- 在工作日程中添加这一条，以便下次与此人见面时进行探讨
				- 直接与他对话，当面或者打电话、发短信或即时信息都行
				- 在每份委派给他人处理的任务上标明具体的日期，这一点非常重要
				- 等待清单
				- 你的下一步行动目前正是别人职责范围内的工作，写下提醒信息，放入“悬而未决”篮中
			- 推迟执行（大部分还要由你亲自实施，而且耗时往往超过2分钟）
				- “悬而未决”中的事务是由“委派他人”和“推迟执行”的行动组成的
				- 如果你决定执行的“下一步行动”无法达成你的预期目标，你就需要以某种方式来提醒自己，直到你最终实现它
	- 组织整理[^12]：建立好清单[^13]、处理行动提示信息
		- 准备建议：核查清单
			- 为了促使这个系统正常运转，你必须做到每日检查和更新
			- 列出有关某个主题、程序或者某种关注/行动领域的内容，供你在特定时间或参与特定类型行动的任何时间使用，帮助你关注项目、工作流程和程序、事件、爱好、职责等领域中可能出现的潜在问题
			- 慢慢地习惯于使用各种核查清单，包括特殊的、临时性的和永久性的核查清单；根据需要随时创建和删除核查清单
		- 有刚性时间要求
			- 录入日程表
		- 有截止日期
			- 录入“等待”清单
		- 处理不确定何时进行的事务
			- 录入日程表
				- 提醒自己开始落实某个项目
				- 你可能想要参与的活动
				- 提醒自己做出决定
					- 你需要做出一个非常重要的决定，但是又无法（或不希望）立刻就做出这个决定
					- 为了能使不做决定成为适当举措，你最好要撒出一张可靠的安全网，对未来可能出现的相应问题保持关注
			- 录入“将来/也许”清单
		- ASAP事务
			- 处理参考资料[^16]
				- 思考要保存多少资料，需要多大空间，以何种形式保存，要保存在什么地方
			- 处理项目辅助资料[^17]
				- 想法和念头属于“项目辅助资料”的范畴
				- 无论如何，你要保证你在回顾项目的时候，能够看到你的这些想法，并能对其善加利用
				- 当你发现某些想法变得无用以后就尽快删除它们吧，以免让这些旧的想法扰乱你的思绪
				- 不要把项目辅助资料作为项目提示信息
			- 处理项目[^14]类事务
				- 原则
					- 不应该包含项目执行的具体细节或方案
					- 不应该按照重要性、大小、紧急程度来排序
					- 结构随工作、生活重心的变化而变化
					- 宏观掌控工作、生活中的事务，并不用于指导每日工作（每天占据你主要精力的是你的日程表、行动清单，以及突发的特殊事务），每周回顾即可（每周回顾是实现长期承诺和日常行动成功匹配的关键因素）
					- 项目与子项目：将“大项目”列入“项目清单”，然后在“项目支持资料”中收录“子项目清单”，以确保在“每周回顾”中能看到它们
				- “隐藏”项目来源
					- 当前活动[^21]，
					- 较高视野的利益和承诺[^22]
					- 当前的问题和机会（问题[^23]、流程改进[^24]、创意和能力建设[^25]）
				- 按照情景[^18]创建“项目”清单
					- 情境分类的数量取决于需要跟踪的行动的数量、不同行动所要求的环境差异的大小
	- 回顾[^26]：保障系统的有效运行
		- 每周回顾[^27]
			- 确认
				- 收集材料
					- 清空大脑
					- 收集你的奇思妙想和新奇的点子。你的脑中经常冒出的一些新鲜、巧妙、富有创意、发人深省、风险十足的念头
				- 清空收集工具
				- 确保自己能够对当前的行动感觉良好、对管理系统有足够的信任感、不会产生没有必要的担忧
			- 更新
				- 回顾“下一步行动”清单。清除系统中已经完成的行动（不是删除，而是标记为“已完成”），检查将要执行的行动的提示信息，以便记录
				- 回顾过往的日程表数据。仔细检查日程表上两三周之前的详细项目，搜寻未完成或紧急行动项目、参考信息等诸如此类的资料，并将它们重新纳入你当前运行的系统中
				- 回顾即将到来的日程。浏览一下将来的事务安排情况（长期的和短期的），确保对即将发生的事情和约会做出安排和准备
				- 回顾“等待”清单。是否有必要采取任何跟进行动？记录任何下一步行动，检查已经记录的任何内容
				- 回顾“项目”清单。逐一地评估所有的项目、目标和愿景，以确保你目前的管理系统没有遗漏任何一个需要采取行动的项目。浏览任何积极、相关的项目计划、支持材料和任何其他进展材料，以促成新的行动、完成项目、待办事项等。
				- 回顾相关核查清单。基于你的各种约定、利益和职责，是否有你需要或想要去做但尚未完成的其他事宜？
				- 回顾“将来/也许”清单。核查其中有哪些内容变得更值得关注、更具行动价值，将其转化为项目，删除已经存在很久的项目内容
		- “大局”的回顾
			- 某些时候，你必须搞清楚你的宏观目的、长期目标，以及从根本上对你的决定起推动、评估和排序作用的愿景和原则。
			- 无论是在生活还是工作中，如何找到合适的范围、如何做出合适的决定、如何确定合理的时间间隔，都只有一个标准，就是让你自己感到一切都清清楚楚就可以了。
			- 从根本上推进并掌控日常生活的方方面面，能自然而然地为更高层次的事务提供丰富的灵感
			- 更多的目标或许是不必要的，你需要在追求已经付诸行动的目标时有种轻松感，并相信你可以自如地执行任何新目标
	- 执行：选择最合适的行动
		- “四标准法”：确定某一时刻的具体行动
			- 情境
			- 有多少时间
			- 有多少精力
			- 重要性
				- “六层次法”：总体检视工作
					- 5楼视野[^32]：人生
					- 4楼视野[^31]：长期展望
					- 3楼视野[^30]：1~2年的目标
					- 2楼视野[^29]：关注和责任范围
					- 1楼视野[^28]：项目
					- 地面：目前的行动
				- 所有事情的重要程度都是由它的上一层次所决定的，因此人们都会认为采用自上而下的思考方式会更有效率；但盲目地采用自上而下的思考方式就只能让自己不断受挫，从实用的角度来讲，我建议采用自下而上的方式进行工作
				- 你考虑最多的事项就是你要处理的最重
				- 当你对一个层次的所有内容都已经完全掌控的时候，你会发现自己自然而然地对各件事情孰轻孰重（事情的优先级）有了一个清醒的认识，而在此之前，你是不可能达到这种状态的
				- 要想确定“重要性”和“优先级”，前提条件就是掌握所有事务，而且需要对其在多个层次上进行思考和归纳
		- 控制项目
			- 需要计划的项目
				- 已经决定了下一步行动，却仍然吸引你注意力的事务
				- 意外冒出新点子或临时出现新细节的项目
			- 采用自然计划法（大多数项目无需计划）
			- 思考方向
				- 使工作更加清晰-->“向上”思考
				- 落实更多的行动-->“向下”思考
			- 将计划本身作为提示信息，写入对应行动清单
			- 某个任务在结束之前，可能会多次在不同的清单中来回转移
- 纵向管理[^10]：自然计划法[^35]
	- 定义目标和原则
		- 思考“为什么？”
		- 思考“我可以完全放手让别人去处理这件事情，只要他们……”等类似问题
	- 展望结果[^34]
		- 思考“如果……那会……？”
	- 头脑风暴/集思广益[^33]
		- 大脑自发进行的（个人头脑风暴）
		- 组织会议集思广益、收集更多信息
	- 归类整理
		- 明确关系（当你完成捕捉想法后）
		- 工具
			- 思维导图、分级概要（大纲）-->明确了主要和次要因素、先后次序以及事务的轻重程度时
			- 甘特图-->按时间注明工作进程，以及一些相对独立的或相关的事件和里程碑，并明确它们与整个计划之间的相互关系
		- 技巧
			- 不判断、不质疑、不评估、不批判（问题解决过程中大脑某一阶段的特定活动）
			- 追求数量、不求质量
			- 把分析组织工作置于次要的地位
	- 明确下一步行动
		- 如果你对某件事情感到一筹莫展，就表明自然计划法前期某个阶段的工作没有到位，还有待进一步加强和提高。
		- 在一件事情的诸多头绪中，总能找到唯一的“下一步行动”，而这个行动正是其他行动的关键条件
		- 你到底需要多翔实的计划？需要详细到什么程度呢？只要能够让你的大脑摆脱事务的纠缠就足够了
- 邮件工作管理系统[^19]
	- 其本身就是最为有效的行动提示，使用它们本身要比把它们列入清单的效果更好
	- 以邮件系统直接打造GTD[^20]管理系统
	- 在你的个人管理系统中，你应该可以在任何需要的时候看到需要的行动提示，这正是个人管理系统和个人行为的基础

---
[^1]: 归拢需要给出某种评估、决定或采取某项行动的项目和想法。又见“捕获”。
[^2]: 准确判定某个已捕获或已收集项目的意义、其衍生物的性质，以及你打算对结果如何处理。又见“清理”。
[^3]: 基本上涵盖了任何不能纳入专用归档系统的东西；一般性归档系统应该触手可及。
[^4]: 用来保存各种有明确分类，并且可以建立索引的资料。
[^5]: 存在于某人的物理或心理环境中、需要就其做出决定，但尚未予以确定或组织的任何内容。
[^6]: 必须做的、应该做的、想做的、等待处理的、需要改进的、引起注意的一切事务，或引起注意的想法、主意等信息。
[^7]: 确认在某个环境中出现并存在的来自捕获阶段的事项的意义（如：“我现在是否需要为此做些什么，如果是，做些什么呢？它是不是可作参考？它是不是垃圾？是否可以留待以后回顾？”），又见“程序”。
[^8]: 搜集（有时生成）被认为具有潜在意义且某人有意就其做出决定或采取行动的项目和想法。又见“收集”。
[^9]: 对处于某一同等水平的内容实施评估和管理（如：回顾某人的所有个人和职业项目）。
[^10]: 在特定范围内检查并创建多层次内容（如：基于预期目标通过下一步行动规划一个项目）。
[^11]: 推动完成某个事项的下一步有形及可见的行为。它非常具体，足以让你明白其实施时间、适用工具（如果有的话）以及可能的“执行”情形。
[^12]: 以物理、可见或数字方式，将意义类似的事项划分至各自独立的类别和位置（如：列表上要打的电话，书架上想读的书，清单上要完成的项目）；组织整理意味着让某个事项所处的位置与其意义相匹配。
[^13]: 用来提示或评估可选择步骤、应遵循程序或行动要素的所有列表（如：旅行清单；电脑备份程序；孩子们的入学筹备任务），指一群具有相似特征并被归为同类的事情的集合。
[^14]: 是指你所承诺的、需要完成多步行动才能达到的目标。项目不可被执行，可执行的是行动。
[^15]: 一个非常简单的文件夹系统，它允许你将未来需要的各种提示信息收入其中，并在指定的日期展示在你面前，就好像这些提示信息会“自动”地出现在你的工作篮中一般。它就像是一个三维立体的日程表。
[^16]: 不需要采取任何行动，但是它们包含了一些信息，出于各种原因，你需要保留。
[^17]: 不是项目的行动信息，也不是项目提示信息，它只是支持项目行动和决策的辅助信息。不能与行动、提示信息混在一起。
[^18]: 完成该行动所需要的工具、地点或者情形。
[^19]: 工作是以电子邮件为核心（每天接收大量邮件，大部分时间花在处理邮件上）。
[^20]: “搞定”的首字母缩略词；本方法论的缩写。
[^21]: 有些项目往往需要从你简单保存的日程表、行动清单和工作区中收集。
[^22]: 你或许有一个好机会，可以从更长远和更重要的角度，即职责、目标、愿景及核心价值观，来敏锐地关注你的某些项目和兴趣。
[^23]: 当你将某个事项评估为问题，而不是评估为可以直接接受的事项本身时，你就是在假设有一个潜在解决方案。是否存在解决方案有待确定，但你至少需要做些探索研究。
[^24]: 无论是在职业还是个人领域，你的管理、维护和工作流程中总会有潜伏项目，事情搞定（或没搞定）的程度如何？在你的归档、存储、沟通、招聘、跟踪或记录程序中，有什么事令人沮丧？在你的个人或业务费用报告、银行业务或投资流程方面，是否有任何地方需要完善？你如何与朋友和家人保持联系？
[^25]: 或许还有一些事项是你一直告诉自己要了解或体验的，以便促进你的自身发展或创意表现，你想学意大利烹饪或是画画儿吗？你是否一直在告诉自己，最好去学习有关数字摄影或社交媒体营销的网上课程？
[^26]: 基于一致性或根据需要分析适当的规划，以便予以澄清和关注。又见“反映”、“地图”。从更广阔的角度评估任何视野或类型的项目内容。
[^27]: 执行收集、理清、组织、回顾所有那些待定的承诺、意图和倾向。如果你的工作是周一至周五的标准五天制，那么我建议你在最后工作日下午留出两个小时的时间，专门用于每周回顾。
[^28]: 我们承诺在明年内完成、要求采取一个以上具体步骤的任何事项。包括拟实现的“修理刹车灯”等短期结果和“重组西部区域办事处”等较大规模的项目。
[^29]: 我们需要维持的生活和工作内容，旨在确保我们自身和我们企业的稳定和健康（如：健康、财务、客户服务、战略规划、家庭、事业）。
[^30]: 拟实现的中期至长期结果（通常在3~24个月内）；如：“完成收购Acme咨询公司”、“为我们的领导力培训课程创建可盈利的在线版本”、“完成玛丽亚的大学规划”。
[^31]: 长期预期结果；理想的完胜方案（如：“出版我的回忆录”、“实现公司上市”、“在普罗旺斯搞个度假屋”）。
[^32]: 一个人或一个企业的终极目标、存在理由和核心价值（如：“为绝大多数市民持续提供最好的商品，服务于社区发展”）。
[^33]: 当你头脑中浮现出的愿景与你目前的现状有所不同时，大脑会自动开始填补这一差距（头脑风暴）；同时，一些想法便会跌跌撞撞地进入你的头脑中，且毫无章法可循——琐碎的、重要的，普通的、绝妙的，都混杂在一起。
[^34]: 为了能够最富有成效地利用你可以获取的资源（无论是那些你已经意识到的，还是尚未认知的资源），你的大脑里都必须清楚地勾勒出一幅成功的蓝图：成功将是一番什么样的景象？听上去会怎样？有什么感觉？
[^35]: 大脑在实现所有预期结果时所遵循的五个阶段的本能思维过程