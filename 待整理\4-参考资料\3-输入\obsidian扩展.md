---
Project:
  - 项目：仓储工作经验总结
---
- `outliner`  ^72b4b7
	- ctrl+shift+↑ 快速向上移动光标所在级别大纲内容
	- ctrl+shift+↓快速向下移动光标所在级别大纲内容
	- 可以在大纲的同层级进行快速上下调整位置

- `slurp`
	- 功能：将网页内容转换为MD文件并存放在OB指定文件夹内
	- [github地址](https://github.com/inhumantsar/slurp)
	- 插件设置
		- Default save location（转存至OB文件夹地址）
		- Manage properties 属性管理（选择对应属性`Edit`取消`Enable property`按钮，可取消存储次属性对应内容）
	- 使用方法
		- [Slurp 文档](https://inhumantsar.github.io/slurp/)
		- 使用指定`URL`设置创建一个新书签（`javascript:(() => document.location.href=`obsidian://slurp?url=${document.URL}`)();`）
		- 找到想要的网页，点击书签即可

- `搜狗输入法`
	- 功能：快速插入自定义emoji表情
	- 设置：菜单-->更多设置-->高级-->自定义短语-->自定义短语设置-->添加新短语
	- 注意：在添加emoji表情时文本框中的内容显示会异常，但不会影响功能使用

