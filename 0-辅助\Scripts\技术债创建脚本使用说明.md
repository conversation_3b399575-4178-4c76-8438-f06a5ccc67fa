# 技术债创建脚本使用说明

## 功能概述

这个脚本可以自动创建技术债文件，具有以下功能：

1. **自动提取项目名称**：从当前活动文件的路径中自动提取项目名称
2. **智能文件命名**：按照 `td-YYYYMMDD-序号.md` 格式命名
3. **自动目录管理**：在 `3-过程资产/项目名称/技术债` 目录中创建文件
4. **模板应用**：使用预定义的技术债模板
5. **自动打开**：创建后在新标签页中自动打开新文件
6. **用户输入别名**：弹出对话框让用户输入技术债别名
7. **自动设置属性**：自动设置创建日期和默认状态

## 文件结构

```
0-辅助/
├── Scripts/
│   ├── createTechDebt.js                # 主要脚本逻辑
│   └── 技术债创建脚本使用说明.md          # 本说明文件
└── Templater/
    ├── Function/
    │   └── createTechDebt.md            # Templater调用接口
    └── Notes/
        └── TP-Project-技术债.md         # 技术债模板
```

## 使用方法

### 执行步骤

1. **在任意项目文件中**（确保路径格式为 `2-项目/项目名称/...`）
2. **执行Templater模板**：`0-辅助/Templater/Function/createTechDebt.md`
3. **输入别名**：在弹出的对话框中输入技术债的别名
4. **自动完成**：脚本自动创建文件并在新标签页中打开

### 路径要求

脚本要求当前活动文件的路径符合以下格式：

```
2-项目/[项目名称]/[子目录]/[文件名].md
```

例如：
- `2-项目/个人知识管理系统/2-每日执行/Do-2025-07-01.md`
- `2-项目/仓储管理系统/1-每周规划/Plan-2025-WK27.md`

## 生成的文件

### 文件位置
`3-过程资产/[项目名称]/技术债/td-YYYYMMDD-XX.md`

### 文件命名规则
- `td-`：固定前缀
- `YYYYMMDD`：当前日期（如：20250703）
- `XX`：当天的序号（01, 02, 03...）

### 示例文件名
- `td-20250703-01.md`（当天第一个技术债）
- `td-20250703-02.md`（当天第二个技术债）

## 模板内容处理

脚本会使用 `TP-Project-技术债.md` 作为模板，并自动：

1. **设置创建日期**：`created_Date: 2025-07-03`
2. **填充别名**：`aliases: ["用户输入的别名"]`
3. **设置默认状态**：`status: 待处理`
4. **保留其他属性**：保持模板的其他结构和内容

### YAML前置元数据示例

```yaml
---
created_Date: 2025-07-03
aliases: ["支付日志性能优化"]
type: 阻塞型/成本型/战略型/无害型
Priority: 立即/高/中/低
status: 待处理
---
```

## 错误处理

脚本包含以下错误处理：

1. **文件路径检查**：如果当前文件不在 `2-项目` 目录下，会显示错误提示
2. **模板文件检查**：如果模板文件不存在，会显示错误提示
3. **目录创建**：如果目标目录不存在，会自动创建
4. **别名输入验证**：如果用户未输入别名或取消输入，会停止创建
5. **文件冲突**：自动处理同名文件，通过序号区分

## 配置要求

### Templater插件配置

1. 启用Templater插件
2. 在设置中配置用户脚本文件夹：`0-辅助/Scripts`
3. 确保模板文件夹包含：`0-辅助/Templater`

### 文件夹结构要求

确保以下文件夹存在：
- `2-项目/`（项目文件夹）
- `3-过程资产/`（过程资产文件夹）
- `0-辅助/Templater/Notes/`（模板文件夹）

## 故障排除

### 常见问题

1. **"未找到当前活动文件"**
   - 确保有文件处于活动状态
   - 重新打开文件后再试

2. **"无法从当前文件路径中提取项目名称"**
   - 检查文件是否在 `2-项目/项目名称/` 目录下
   - 确保路径格式正确

3. **"模板文件不存在"**
   - 检查 `0-辅助/Templater/Notes/TP-Project-技术债.md` 是否存在
   - 确保文件路径正确

4. **"未输入别名，取消创建"**
   - 在对话框中输入有效的别名
   - 不能留空或取消输入

### 调试信息

脚本会在控制台输出调试信息：
- 当前文件路径
- 提取的项目名称
- 创建的目录和文件路径

可以通过开发者工具查看这些信息。

## 使用示例

### 完整使用流程

1. **打开项目文件**：如 `2-项目/个人知识管理系统/2-每日执行/Do-2025-07-01.md`
2. **执行脚本**：运行 `createTechDebt.md` 模板
3. **输入别名**：在对话框中输入"支付日志性能优化"
4. **查看结果**：
   - 创建文件：`3-过程资产/个人知识管理系统/技术债/td-20250703-01.md`
   - 在新标签页中打开文件
   - YAML属性已自动设置

### 生成的文件内容

```yaml
---
created_Date: 2025-07-03
aliases: ["支付日志性能优化"]
type: 阻塞型/成本型/战略型/无害型
Priority: 立即/高/中/低
status: 待处理
---
# 1. 基础信息

- 发现场景：支付高峰期生产环境日志线程阻塞导致支付超时
- 发现位置：`src/payment/service/PayLogUtil.java`
- 根本原因：同步日志写入设计缺陷
...
```

## 更新日志

- **v1.0**（2025-07-03）：初始版本，支持基本的技术债创建功能
