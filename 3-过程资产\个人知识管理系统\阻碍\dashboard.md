---
aliases:
---

```dataviewjs
const projectName = dv.current().file.path.split("/")[1];
const folderPath = `3-过程资产/${projectName}/阻碍`;
const files = dv.pages(`"${folderPath}"`)
	.sort(p => p.file.mtime,"desc")
	.filter(p => p.file.name !== dv.current().file.name);
dv.table(
    ["序号", "文件链接", "别名", "状态", "relation"],
    files.map((p, index) => {
        // 处理别名字段（数组转字符串）
        const aliases = p.aliases 
            ? (Array.isArray(p.aliases) ? p.aliases.join(", ") : p.aliases)
            : "-";
        // 处理relation字段（数组转字符串）
        const relations = p.relation 
            ? (Array.isArray(p.relation) ? p.relation.map(r => r).join(", ") : p.relation)
            : "/";
        return [
            index + 1,                      // 自动编号
            p.file.link,                    // 文件链接
            aliases,                        // 别名
            p.status || "-",                // 状态
            relations                       // 关系链接
        ];
    })
);
```