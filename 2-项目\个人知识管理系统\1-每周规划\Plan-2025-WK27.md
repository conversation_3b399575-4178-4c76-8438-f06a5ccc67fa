---
homePageLink: "[[PKMS-首页]]"
上周复盘: "[[Replay-2025-WK26]]"
---
# 1. [[字段提示信息表#周目标描述： f37322|周目标]] 

- 在下个迭代前，完成可在项目组件之间流畅运行的障碍管理工作流的搭建
- 在下个迭代前，完成可在项目组件之间流畅运行的技术债管理工作流的搭建

# 2. [[字段提示信息表#验收标准描述 19887e|验收标准]] 

| 交付物          | 标准/通过条件                                                                                                                                                                                                                                                          | 备注  |
| ------------ | ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | --- |
| 「障碍日志」模板V1.0 | **使用模板进行障碍记录时，未出现以下任一情况**<br>（1）「不知道该填写什么/下一步做什么」导致流程中断<br>（2）出现3次以上「这个信息该填在哪里？」的疑问<br>（3）选择主动跳过模板中某个模块（因认为「无用」或「太复杂」）<br>**必须具备以下核心功能**<br>（1）必须作为障碍记录为唯一文件<br>（2）必须有创建日期、状态、类型、别名                                                                              |     |
| 「每周计划」模板V2.0 | **使用组件进行障碍、技术债管理时，未出现以下任一情况**<br>（1）需要手动搜索才能查询到上周未解决的障碍、技术债信息<br>（2）发现影响障碍时不能立即调整任务优先级、拆解工作量、调整周目标<br>**技术债模块必须具备的核心功能**<br>（1）可以根据技术债对KR或者周目标的影响度进行优先级排序<br>（2）快速定位高优先级技术债详情页面<br>（3）完成技术债任务拆解后，可自动更新至“每周计划”任务拆解标题下，并可以明显区分普通任务                                 |     |
| 「每日执行」模板V2.0 | **使用组件进行障碍管理时，未出现以下任一情况**<br>（1）不能立即记录新发现的障碍？或不知如何记录？<br>**障碍模块必须具备的核心功能**<br>（1）以嵌入的形式展示在任务看板中<br>（2）显示阻塞状态 + 摘要内容 + 关联类型（任务/每周计划/整体项目）<br>（3）点击可跳转至障碍日志详情页<br>**技术债模块必须具备的核心功能**<br>（1）显示本周已经创建的技术债任务<br>（2）能明显与普通任务区分开<br>（3）使用快捷键自动在指定路径下创建符合要求的技术债（文件名、内容）  |     |
| 「每周评审」模板V2.0 | **使用组件进行障碍管理时，未出现以下任一情况**<br>（1）需要手动翻看本周历史文件才能查看本周新增相关障碍信息<br>**必须具备的核心功能**<br>（1）可以直观查看“目标成果”、“技术债成果”、“阻碍成果”<br>（2）可以直观查看3类成果对应的未完成专项任务<br>（3）可以对技术债任务的不同完成状态进行评估<br>（4）顺利完成的技术债任务可以体现出对KR或周目标的影响<br>（5）包含可复用知识<br>（6）未完成技术债任务需要有对应的根因分析和改善<br>（7）可以显示具体的改善行动内容 |     |
| 「每周评审」模板V2.0 | **使用组件进行障碍管理时，未出现以下任一情况**<br>（1）需要人工统计才能查看障碍类型的统计信息（本周+历史）<br>（2）需要手工搜索障碍日志来查看本周新增障碍信息<br>**技术债模块必须具备的核心功能**<br>（1）针对未完成的技术债，可以从流程角度分析原因<br>（2）可以体现具体的流程改进措施<br>（3）展示流程改进的历史进展，以形成闭环                                                                             |     |
| 「技术债记」模板V1.0 | **使用模板进行技术债记录时，未出现以下任一情况**<br>（1）「不知道该填写什么/下一步做什么」导致流程中断<br>（2）出现3次以上「这个信息该填在哪里？」的疑问<br>（3）选择主动跳过模板中某个模块（因认为「无用」或「太复杂」）                                                                                                                                          |     |
| 「技术债工作流」     | （1）必须与敏捷组件相融合且逻辑融洽、顺畅<br>（2）必须保留技术债管理的核心关键                                                                                                                                                                                                                       |     |
# 3. 障碍回顾
![[看板工具#^c68f7b]]

# 4. 技术债
![[看板工具#^a87b98]]

# 5. 任务拆解

- [x] 探索障碍管理的核心特点，并优化现有的“障碍日志”模板 ⏳ 2025-06-30 ✅ 2025-06-30
- [x] 探索障碍管理的工作流的细节，并追加优化验收标准 ⏳ 2025-06-30 ✅ 2025-06-30
- [x] 梳理历史阻碍，并将其全部转换为障碍日志 ⏳ 2025-06-30 ✅ 2025-07-01 ^ef9db3
- [x] 设计并优化【每周计划】组件模板及其辅助文件 ⏳ 2025-07-01 ✅ 2025-07-01
- [x] 设计并优化【每日执行】组件模板及其辅助文件 ⏳ 2025-07-01 ✅ 2025-07-01
- [x] 设计并优化【每周评审】组件模板及其辅助文件 ⏳ 2025-07-01 ✅ 2025-07-01
- [x] 设计并优化【每周回顾】组件模板及其辅助文件 ⏳ 2025-07-02 ✅ 2025-07-02
- [x] 探索验证使用“技术债记录模板”管理技术债方案的总体可行性 ⏳ 2025-07-02 ✅ 2025-07-02
- [x] 探索技术债管理的核心特点，并创建“技术债记录”模板 ⏳ 2025-07-02 ✅ 2025-07-03
- [x] 探索技术债管理的工作流的细节，并追加、优化验收标准 ⏳ 2025-07-03 ✅ 2025-07-03
- [x] 优化【每周计划】组件模板“技术债”模块部分 ⏳ 2025-07-03 ✅ 2025-07-03
- [x] 优化【每日执行】组件模板“技术债”模块部分 ⏳ 2025-07-04 ✅ 2025-07-04
- [x] 优化【每周评审】组件模板“技术债”模块部分 ⏳ 2025-07-04 ✅ 2025-07-05
- [x] 优化【每周回顾】组件模板“技术债”模块部分 ⏳ 2025-07-05 ✅ 2025-07-05