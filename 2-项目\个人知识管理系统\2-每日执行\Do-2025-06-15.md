---
homePageLink: "[[PKMS-首页]]"
本周计划: "[[Replay-2025-WK24]]"
---
# 1. 任务安排
> [!dashboard]
> 
> > [!todo] 今日代办
> > ```tasks
> > hide backlink
> > filter by function \
> > const fileName = query.file.filenameWithoutExtension;\
> > const dateMatch = fileName.match(/\d{4}-\d{2}-\d{2}/);\
> > if (!dateMatch) return false;\
> > const targetDate = moment(dateMatch[0]);\
> > const year = targetDate.isoWeekYear();\
> > const weekNumber = targetDate.isoWeek().toString().padStart(2, '0');\
> > const targetPath = `Plan-${year}-WK${weekNumber}`;\
> > const pathMatches = task.file.path.includes(targetPath);\
> > const titleKeywords = "关联任务"; /* 自定义关键词 */\
> > const headingMatches = task.heading && task.heading.includes(titleKeywords);\
> > let dateMatches = false;\
> > if (task.scheduled && task.scheduled.moment) {\
> > dateMatches = task.scheduled.moment.isSame(targetDate, 'day')}\
> > return pathMatches && headingMatches && dateMatches;
> > ```
> 
> > [!tip] 未规划
> > ```tasks
> > not done
> > no scheduled date
> > hide backlink
> > filter by function \
> > const fileName = query.file.filenameWithoutExtension;\
> > const dateMatch = fileName.match(/\d{4}-\d{2}-\d{2}/);\
> > if (!dateMatch) return false;\
> > const targetDate = moment(dateMatch[0]);\
> > const year = targetDate.isoWeekYear();\
> > const weekNumber = targetDate.isoWeek().toString().padStart(2, '0');\
> > const targetPath = `Plan-${year}-WK${weekNumber}`;\
> > const pathMatches = task.file.path.includes(targetPath);\
> > const titleKeywords = "关联任务"; /* 自定义关键词 */\
> > const headingMatches = task.heading && task.heading.includes(titleKeywords);\
> > const hasContent = task.description?.trim().length > 0;\
> > return pathMatches && headingMatches && hasContent;
> > ```

# 2. 阻碍/[[字段提示信息表#技术债 7ffbc6|技术债]] 

- 使用`git`插件进行版本管理时，不能直接查看当前活动文件（单个文件）的所有历史版本信息 #阻碍 
- “问题记录”模板中使用简单的“5why分析法”进行“根因分析”，没有严谨框架 #技术债 


# 2. 输出

- [[知识管理价值流程图]] 
- [[字段提示信息表]] 
- [table-style.css]
- [[TP-Project-Plan]]
- [[TP-Project-Do]] 
- [[看板工具]] 
- [[TP-Knowledge-问题]]
- [[TP-Knowledge-方法]]
- [[TP-Knowledge-案例]]
- [[TP-Knowledge-Sop]]
- [分栏.css]
- [[TP-Project-Replay]]