/* 核心元数据网格布局 - 仅保留网格布局功能 */
body .markdown-source-view.mod-cm6 .metadata-properties,
body .markdown-preview-view .metadata-properties {
    display: grid;
    gap: 1em;
}

/* 默认列数：1列（当未指定c类时） */
body .markdown-source-view.mod-cm6 .metadata-properties:not([class*="c"]),
body .markdown-preview-view .metadata-properties:not([class*="c"]) {
    grid-template-columns: repeat(1, 1fr);
}

/* 列数控制 */
body .markdown-source-view.mod-cm6.c2 .metadata-properties,
body .markdown-preview-view.c2 .metadata-properties {
    grid-template-columns: repeat(2, 1fr) !important;
}

body .markdown-source-view.mod-cm6.c3 .metadata-properties,
body .markdown-preview-view.c3 .metadata-properties {
    grid-template-columns: repeat(3, 1fr) !important;
}

body .markdown-source-view.mod-cm6.c4 .metadata-properties,
body .markdown-preview-view.c4 .metadata-properties {
    grid-template-columns: repeat(4, 1fr) !important;
}

body .markdown-source-view.mod-cm6.c5 .metadata-properties,
body .markdown-preview-view.c5 .metadata-properties {
    grid-template-columns: repeat(5, 1fr) !important;
}

body .markdown-source-view.mod-cm6.c6 .metadata-properties,
body .markdown-preview-view.c6 .metadata-properties {
    grid-template-columns: repeat(6, 1fr) !important;
}