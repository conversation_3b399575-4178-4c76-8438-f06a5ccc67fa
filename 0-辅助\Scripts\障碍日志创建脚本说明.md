# 障碍日志创建脚本使用说明

## 功能概述

这个脚本可以自动创建项目障碍日志文件，具有以下功能：

1. **自动提取项目名称**：从当前活动文件的路径中自动提取项目名称
2. **智能文件命名**：按照 `blocker-YYYYMMDD-序号.md` 格式命名
3. **自动目录管理**：在 `3-过程资产/项目名称` 目录中创建文件
4. **模板应用**：使用预定义的障碍日志模板
5. **自动打开**：创建后自动打开新文件

## 文件结构

```
0-辅助/
├── Scripts/
│   ├── createdBlocker.js          # 主要脚本逻辑
│   └── 障碍日志创建脚本使用说明.md    # 本说明文件
└── Templater/
    ├── Function/
    │   └── createdBlocker.md       # Templater调用接口
    └── Notes/
        └── TP-Project-障碍日志.md   # 障碍日志模板
```

## 使用方法

### 方法一：通过Templater插件

1. 在任意项目文件中（路径格式：`2-项目/项目名称/...`）
2. 使用Templater插件执行模板：`0-辅助/Templater/Function/createdBlocker.md`
3. 脚本会自动：
   - 提取当前文件路径中的项目名称
   - 在对应的 `3-过程资产/项目名称` 目录中创建障碍日志文件
   - 打开新创建的文件

### 方法二：直接调用JavaScript函数

如果您配置了Templater的用户脚本功能：

```javascript

```

## 路径要求

脚本要求当前活动文件的路径符合以下格式：

```
2-项目/[项目名称]/[子目录]/[文件名].md
```

例如：
- `2-项目/个人知识管理系统/2-每日执行/Do-2025-07-01.md`
- `2-项目/仓储管理系统/1-每周规划/Plan-2025-WK27.md`

## 生成的文件

### 文件位置
`3-过程资产/[项目名称]/blocker-YYYYMMDD-XX.md`

### 文件命名规则
- `blocker-`：固定前缀
- `YYYYMMDD`：当前日期（如：20250701）
- `XX`：当天的序号（01, 02, 03...）

### 示例文件名
- `blocker-20250701-01.md`（当天第一个障碍日志）
- `blocker-20250701-02.md`（当天第二个障碍日志）

## 模板内容

脚本会使用 `0-辅助/Templater/Notes/TP-Project-障碍日志.md` 作为模板，并自动：

1. 设置创建日期
2. 保留模板的所有结构和示例内容
3. 应用YAML前置元数据

## 错误处理

脚本包含以下错误处理：

1. **文件路径检查**：如果当前文件不在 `2-项目` 目录下，会显示错误提示
2. **模板文件检查**：如果模板文件不存在，会显示错误提示
3. **目录创建**：如果目标目录不存在，会自动创建
4. **文件冲突**：自动处理同名文件，通过序号区分

## 配置要求

### Templater插件配置

1. 启用Templater插件
2. 在设置中配置用户脚本文件夹：`0-辅助/Scripts`
3. 确保模板文件夹包含：`0-辅助/Templater`

### 文件夹结构要求

确保以下文件夹存在：
- `2-项目/`（项目文件夹）
- `3-过程资产/`（过程资产文件夹）
- `0-辅助/Templater/Notes/`（模板文件夹）

## 故障排除

### 常见问题

1. **"未找到当前活动文件"**
   - 确保有文件处于活动状态
   - 重新打开文件后再试

2. **"无法从当前文件路径中提取项目名称"**
   - 检查文件是否在 `2-项目/项目名称/` 目录下
   - 确保路径格式正确

3. **"模板文件不存在"**
   - 检查 `0-辅助/Templater/Notes/TP-Project-障碍日志.md` 是否存在
   - 确保文件路径正确

### 调试信息

脚本会在控制台输出调试信息：
- 当前文件路径
- 提取的项目名称
- 创建的目录和文件路径

可以通过开发者工具查看这些信息。

## 更新日志

- **v1.0**（2025-07-01）：初始版本，支持基本的障碍日志创建功能
