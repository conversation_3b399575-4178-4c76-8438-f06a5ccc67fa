---
Project: 项目：仓储工作经验总结
---
## 行动

- [x] 设定任务目标
	- [[SMART原则]] 
- 记录实践中的问题
- 根据常见问题总结“防弹笔记”运行规则
- “清空大脑”，收集“噪声”
	- 使用什么工具更方便？
		- 印象笔记页面太复杂，可以尝试使用flomo来收集这些内容
	- 每一个“噪声”可能是想法、灵感、问题、行动、任务、项目，需要在处理时进行区分
	- 在收集放置“噪声”时，当已知晓与某个项目相关，但是在项目的任务、行动未明确时，可直接放在这个相关的项目笔记的空白位置，方便后续进行转写
	- ~~对【临时笔记】的内容进行排程时，待办、交办最好单独列成一则核心任务笔记或者行动，这样更符合防弹笔记法的逻辑~~
		- 排程行动时，应放在与它相关的行动下，且他门之间为相关关系，没有隶属关系
	- “防弹笔记法”是一种知识管理的方法论、是一种工作流程
	- 收集“噪声”时，若一时间无法判断并排程，应立即先将其存放在「暂时笔记」中
- 新建一则「核心任务笔记」
	- 在执行行动时，突然出现关于“笔记运行规则、机制”的问题、思考、灵感，该怎么办？是放在正在执行的某个具体的任务下？还是放在「任务：记录不同情景下做笔记的方法」中？
		- 理论上问题、思考、灵感的出现都是关于执行的某个具体行动的内容，所以这些内容放置的位置还是不变，需要思考的是当前行动的归属；执行的行动是在默认笔记的机制、规则已经清晰的前提下，是为了达成对应项目的目标；除非笔记规则还不清晰且有对应的项目，那就应该将其项目或任务中
			- 行动是为了更好服务任务及项目，笔记运规则应该调整为“输出”形式的数据。它可能出现在任何行动中，强制拆分会导致原任务割裂；每个思考、灵感、问题都应更好的服务当前行动，当出现与“笔记规则”有关的内容时，插入「“防弹笔记”实用手札」链接即可
			- 「“防弹笔记”实用手札」可以与某些行动相关，也可单独编辑
		- 创建任务时容易将其与“输出”的数据混淆
	- 执行自己没有做过的任务会经历哪些步䠫？
	- 针对工作中的任务不知道如何设计阶段性任务成果？
		- 将每一个【核心任务笔记】设定一个“阶段行成果”标题的目的，除了任务需要成果驱动，另一点是任务可能是具有成长性的，当某一个具体的成果达成的差不多时，可能会有进一步的愿景或成果目标。或者一个任务的成果目标依据自己的想法本身就具有阶段性，这样命名会更加直观。
	- 「核心任务笔记」命名时加上「任务：」前缀，使笔记更直观
	- 在行动下方存放想法、问题、思考……等信息或数据时可以在文本前使用emoji表情，可以直观的对这些信息进行分类，方便日后回顾
		- emoji表情添加太麻烦，即使使用`Icon Shortcodes` 插件也还是太复杂
			- emoji表情添加后，“如何分类”变成了另外一个问题，目前还没有找到更好的分类标准？
	- 如何快速调整大纲文本行的位置？
		- [[obsidian扩展#^72b4b7]] 
	- 在一则笔记的行动下面记录的问题、想法、提示……的内容，当在复用行动（执行重复的任务）时就没有必要再写一次了，因为实际上在复用时，已经把它当做一种经验在运用了?
	- 引号：“”（双引号）、‘’（单引号），还有另一种书写方式「」和『』（直角引号），用法与前面相同。可以表示对别人说的话进行引用、表示特殊称谓或专有名词、表示特殊含义、突出强调。在笔记中可以当做表示专有名称使用
		- 在输入法中使用“自定义短语”功能可以提高输入效率
- 安排「核心任务笔记」结构
	- 当有针对某个问题的新想法出现时，衍生的行动应该放在大纲结构的什么位置？
		- 将有关的新想法降低一个层级，放在衍生的行动下发
	- 针对验证某个问题、想法过程中出现的错误方法或想法，为避免后续引起不必要的麻烦、增加阅读量，可在对应位置添加删除线以达到目的
- 新建一则「项目」笔记
	- 建立【项目笔记】文件夹，所有项目笔记文件以「项目：」为前缀命名
	- 放置在行动下的”数据“全部归类在”知识与经验笔记“文件夹好，还是分类存放在”输入“、”输出“中好？
		- 在项目主笔记中，分类存放更直观
	- 在转化核心任务笔记时，如何理解“项目”的概念并加以应用？
		- 按“归类”的概念理解？
			- 目的：将相关的任务集合在一起，方便管理
			- 为何不用文件夹代替？
		- 按“有机组合”的概念理解？
			- 强调任务的有序性、目的性
			- 排出优先次序、排出下一步行动清单（更好的理解我为什么做？怎么做？做什么？）
	- “梳理仓储工作经验”是一个任务、还是一个项目？
		- 在连接时不知道如何安排它的位置？
		- 任务是行动的集合，行动是可以一步完成的任务
	- 对一个新的或不熟悉的「项目」进行分析时，其任务模块通常只能有一个大概的轮廓，不能直接拆解成对应的「核心任务笔记」，也不能架构正确的次序结构？
		- 新项目开始阶段，通常会进行一些尝试性的行动，这些应该在适当的情景下执行，才能节约时间并进一步找到具体的行动方案（创建核心任务笔记），那么它们应该以什么样的形式存放在什么位置？
		- 除非对一个项目或者任务很熟悉，否则没有办法自上而下执行相关行动
	- 如果完成一个项目所需要的技能或知识不足，不能直接开始相关项目，对应不足的部门需要重新立项吗？还是将其归入当前项目中？
		- 无需重新立项，在原项目上设定任务即可。因在当前项目上，目的意图会更明确、能更好的匹配当前项目
- **安排「项目」笔记结构**
	- 「项目」中分解的「核心任务笔记」任务笔记使用任务列表格式，完成后可以直接勾选上，可以直观查看当前的完成进度
	- 项目笔记中可以使用隶属关系的「任务」与「子任务」，但任务中的行动中只能出现相关关系的「行动」（只是相关的「行动」在当前「行动」层级下）
		- 大纲层级限制在二级
- **安排周行动**
	- 原则：
	- 方法步骤：
		- 先看时间提醒：有没有下周特定时间要开始的任务？（功能还未开发）
		- 「重点推进」
			- 2个工作任务或行动
				- 看「重要」标签：哪些项目需要推进进度？
					- ~~标签没有明确区分出是“生活流程”还是“工作流程”？~~
					- ~~当前安排的内容是依据「工作」进度流程来安排的，理论上应该全部属于工作内容~~
			- 3个时段的琐碎任务（早、中、晚3个时段，剩下其他要做的事情，都归类到其他琐事）
				- 来源
					- 临时交办的小事、次要的意外处理
					- 在执行行动时遇到的重要非紧急的问题以及启发的行动
			- 1个个人目标任务或行动
				- “我会利⽤空档去推进这个每天选择好的个⼈⽬标，这可以帮助⾃⼰好好把握住那些突然出现的空档，不⾄于⽩费。”-----（抄录《大脑减压的子弹笔记术》）
			- 根据已经排出来的行动清单，对应任务或行动的时间情景
		- 看「收集箱」：有没有这周收集，还没排程（让任务、⾏动出现在该出现的位置）处理的任务？查看flomo看看还没有需要汇总、整理到OB暂存笔记的信息？
		- 看「计划酝酿」标签：有企划中项目可以推进下一步了吗？有没有需要立项的主题？
			- 包含工作、生活、个人目标3种行动情景
		- 看「定期追踪」标签：有没有需要回顾的任务？
			- 包含工作、生活、个人目标3种行动情景
		- 衡量剩余的空挡时间，根据已经排出的行动清单，对应任务或行动的时间情景
- **执行安排**
	- 执行行动时，如遇到突然出现或联想的任务或行动，应该怎么处理？是打破原来的安排，重新计划？还是先排程，后续再安排
		- 先思考是否影响当日安排的后续任务或行动的执行；若影响进度推进、完成，需要将其添加到当日行动清单中；否则，将其排程至对应项目中即可
	- 执行行动时，如当前行动因为缺少某些条件，无法继续推进，后续行动应该如何调整？
		- 在次要事务类别中，增加对应条件的行动，以辅助暂停行动的继续推进
	- 周日安排行动时，内容尽量少一点琐事的事情，以便有更充裕的时间安排下一周的行动计划
	- 每日中午起床后20-30分钟，精神不好，需要安排一些尽量少动脑子的行动