- 删除[每日执行]页面“目标异动”
- 将“技术债”与“阻碍”进行合并，条目以标签标记进行区分
- 将“闪念”与“输出”进行合并，闪念以任务形式记录，输出以无序列表形式记录

- 删除[每周计划]中的“本周方向”，在笔记属性中增加上周复盘链接


| **字母​**​  | ​**​类型​**​      | ​**​识别关键词​**​ | ​**​下一步动作​**​          |
| --------- | --------------- | ------------- | ---------------------- |
| ​**​A​**​ | 流程优化型 (Auto)    | “重复/麻烦/低效”    | ➤ 写进​**​系统优化清单​**​（蓝色） |
| ​**​P​**​ | 风险预警型 (Prevent) | “万一/可能/担心”    | ➤ 更新​**​风险看板​**​（黄色）   |
| ​**​D​**​ | 创新解法型 (Do)      | “如果/试下/新方法”   | ➤ 存入​**​创新实验库​**​（紫色）  |
| ​**​S​**​ | 自我驱动型 (Self)    | “有趣/想学/关联”    | ➤ 扔进​**​兴趣沙盒​**​（绿色）   |
事件描述：[时间戳] + [主体] + [异常指标]（含基准值） + [直接诱因] + [可量化结果] + [衍生影响]  （检验标准：多严重？为什么发生？何时失控？影响谁？）

项目执行偏差

成果验收

- 对照周初计划，逐项验收已完成的​​可交付成果​​（如：功能模块、用户反馈报告）
- 记录未计划但实际产生的价值（如临时修复生产问题）

为实现短期目标而牺牲长期质量或可维护性的妥协决策或疏漏​​

风险预警：风险、问题、警告、可能、担心、阻碍
需求响应：客户、用户、反馈、想要、需要、请求、Bug
流程优化：流程、步骤、效率、慢、麻烦、自动化、改进
问题探索：为什么、原因、根因、分析、调查、不清楚
创新方案：想法、试试、如果、新方法、创意、假设

```本周方向
	// 获取当前文件的路径信息，提取项目名称
	const currentFile = dv.current().file;
	const currentPath = currentFile.path;
	const projectName = currentPath.split("/")[1].trim();
	
	//获取周数、年
	const weekNumber = Number(dv.current().file.name.split("WK")[1]) -1;
	const year = dv.current().file.name.split("-")[1];
	
	//动态生成文件名
	const dynamicFilename = `Replay-${year}-WK${weekNumber.toString().padStart(2, '0')}.md`;
	const path = `2-项目/${projectName}/3-每周复盘/${dynamicFilename}`;
	const file = app.vault.getAbstractFileByPath(path);
	const targetHeading = "# 6. 下周聚焦";
	if(file){
		//读取并解析文件
		const content = await dv.io.load(path);
		const headingRegex = new RegExp(`(${targetHeading}[^]*?\\n)([^]*?)(?=\\n#|$)`);
		const match = content.match(headingRegex);
		const result = match[2].trim();
		dv.paragraph(result)
	}else{
		dv.el("p","无内容")
	}
```