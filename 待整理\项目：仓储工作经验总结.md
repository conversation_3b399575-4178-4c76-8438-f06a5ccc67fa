---
Scene: 工作
Progress: 重点推进
startDate: 2024-05-20
endDate:
---
## 概览

- 动机
	- 工作了8年的时间，感觉工作好像没有给自己带来什么成果，梳理一下工作经验，把这个内容当做成果给自己一个交代
	- 感觉自己需要一个知识管理系统，自己在工作中也尝试去做知识管理
	- 工作没有做到回顾、总结，等到要用的时候再从头整理，很影像工作节奏和质量，工作效率低
	- 工作没有沉淀已经严重影响到了日常的工作
	- 处理工作有一个不好的习惯，在没有分清楚接下来的计划的前提下，展开的网太大，总是想要在处理问题时企图把之前没有做的工作一并处理掉。导致效率极低，且偏离了现在工作的目的和重心
	- 每次进行双周工作回顾需要花费很长的时间，我需要一种思考方式和解决方案可以缩短时间成本
	- 工作中出现的很多问题、疑惑（可能解决了，也可能没有解决），像一根根刺一样一直扎在心里，好像从来没有被拔出来过。一旦开始工作，感觉总是在从头开始，没有积累，总是想积累却不知道如何积累，一直在寻找方法的路上；当陆陆续续新的工作或重复的工作出现，迫使我不得不停止寻找方法，继续处理当前的事情；无力感在开始、中断、迷茫中反复循坏
- 目的
	- 搭建一个可以解决工作问题、积累工作经验的系统或理论方法，可以解决我的困惑、提升工作效率
- 目标
	- 搭建个人知识管理系统
		- 学习IOTO框架
			- 一直在研究知识管理，逐渐忘记了初衷
	- 学习《防弹笔记法》
		- 强调记录、解决工作中遇到的问题，以此来积累工作经验，并提供了一套方法论

- 梳理个人仓储工作经验 
	- 目的
		- 结合“防弹笔记法”在obsidian中搭建一套合理的工作流
		- 梳理、总结个人仓储工作经验
	- 必要结果
		- “防弹笔记法”是什么？有什么用处？如何与obsidian结合在一起？
		- 结合到个人工作时都有哪些对应的场景？每种场景下如何使用这套方法？
		- 我在工作中都经历过什么？工作时都干过哪些事情？
		- 干质量时（在质量岗位上时）我都做过哪些工作？
		- 完成每项具体的工作时的详细行动步骤是什么？
		- 完成任务或执行每个步骤常见的问题是什么？对应的解决方案是什么？
		- 我在工作中都学习过哪些专业知识？培养过哪些工作技能？这些工作技能是否存在欠缺？具体欠缺的地方在哪里？
	- 目标
		- 掌握“防弹笔记”的运行规则
		- 理解、掌握目标管理（包含但不限于目的、目标、任务、计划、项目）概念的含义及演化关系
## 任务

- 相关书籍和文章
	- [[任务：阅读《如何阅读一本书》]] 
	- [[任务：阅读《防弹笔记法》]] 
	- [[任务：阅读《大脑减压的子弹笔记术》]] 
	- [[任务：理解目标管理]] 
- “防弹笔记”尝试
	- [[任务：尝试创建“防弹笔记”]] 
	- [[任务：设计Project进度查询看板]] 
	- [[任务：优化链接、成果、行动创建方式]] 
- 其他工作经验
	- [[任务：尝试安排周行动]] 
## 输入
```dataviewjs
const project = dv.current().file.name;
dv.list(
	dv.pages('"3-输入"')
	.where(p => p.project?.includes(project))
	.sort(p => p.file.mtime,'desc')
	.file.link
)
```
## 输出
```dataviewjs
const project = dv.current().file.name;
dv.list(
	dv.pages('"4-输出"')
	.where(p => p.project?.includes(project))
	.sort(p => p.file.mtime,'desc')
	.file.link
)
```