## Tips

```mermaid
	flowchart LR
	a([flomo])
	b([1-Idea])
	a--转移-->b
```
## Contents

- 使用task插件创建重复性任务，并使用🏁 delete ，无法重置新建任务的状态
- 增加参考资料的“输入”、“输出”分类，使后续单独查找资料时更加直观
- 若某个项目的一个行动提前完成，可以进入“下一步”，应该立刻行动？还是重新安排时间？
- 搜索信息阶段，可以将关键信息先记录下来，等到“时机成熟”进入“下一步”时，可以更快开始执行，也可以减少大脑记录它们造成的压力，提升效率
- 项目的结构越来越趋近于IOTO，关于“资料”、项目整体中是否需要重新细化分类，需要重点考虑
- 系统学习流程图制作方法
- 如何不通过项目索引快速查找参考资料？
- 在执行“尽快行动”清单中的行动时，发现当前行动尚未明确清晰（仍有“上一步”需要执行或执行时还需思考如何做……）、不确定是项目还是行动等情况，导致思绪混乱（不知该做还是不该做）、效率低下？
- “尽快行动”清单中的行动应该是除了时间（场景）外，其他都已经明确、可以直接执行且无需额外多余思考的
- 项目中的行动应该只包含已确认日期的行动和“将来也许”行动，不应该有“尽快行动”的类型？
- 项目中的行动时完成项目目标的关键动作，非关键动作放在项目中破坏了整体的结构，也许可以放在“尽快行动”清单中？
- 使用软连接将本地文档映射到obsidian中，方便调用查阅同时避免保存重复的文档
- 不同项目的推进频率需要一个工具或者一个方法来更直观、准确的判断和安排
- 当项目的一个行动无法在当天计划时间内完成（无法一步完成），应该如何安排？
- 重新安装office2016（重点安装project）
- 梳理项目清单，识别项目集以及与学习PMP项目的依赖关系