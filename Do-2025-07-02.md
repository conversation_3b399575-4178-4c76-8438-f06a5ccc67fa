
```dataviewjs
// 优化的闪念/输出查询 - 添加初始化检查和错误处理
async function displayFlashIdeas() {
    try {
        // 检查 Dataview 是否已完全初始化
if (!dv || !dv.current || !dv.pages) {
	setTimeout(() => displayFlashIdeas(), 100);
	return;}
        // 1. 从当前文件名提取目标日期
        const currentFile = dv.current();
        if (!currentFile?.file?.name) {
            dv.paragraph("⚠️ 无法获取当前文件信息");
            return;
        }
        const currentFileName = currentFile.file.name;
        const dateMatch = currentFileName.match(/Do-(\d{4}-\d{2}-\d{2})/);
        if (!dateMatch) {
            dv.paragraph("⚠️ 无法从文件名中提取日期");
            return;
        }
        const targetDate = dateMatch[1];
        const projectName = currentFile.file.path.split("/")[1];
        if (!projectName) {
            dv.paragraph("⚠️ 无法提取项目名称");
            return;
        }
        // 2. 查询闪念笔记（假设存储在特定位置）
        const ideaPath = `1-Inbox`; // 根据实际情况调整路径
        // 3. 创建安全的日期解析函数
        const parseDate = (value) => {
            try {
                if (value?.isValid) return value;
                const dateStr = value?.toString() || "";
                // 解析多种日期格式
                const formats = [
                    /(\d{1,2}) (\d{2}), (\d{4})/, // "6 02, 2025"
                    /(\d{4})-(\d{2})-(\d{2})/, // "2025-06-02"
                ];
                for (const format of formats) {
                    const match = dateStr.match(format);
                    if (match) {
                        if (format === formats[0]) {
                            const [, month, day, year] = match;
                            return dv.date(`${year}-${month.padStart(2, '0')}-${day}`);
                        } else {
                            return dv.date(dateStr);
                        }
                    }
                }
                return dv.date(dateStr);
            } catch (error) {
                console.warn("日期解析错误:", error);
                return null;
            }
        };
        // 4. 查询今日的闪念记录
        const targetDateObj = dv.date(targetDate);
        if (!targetDateObj?.isValid) {
            dv.paragraph("⚠️ 目标日期无效");
            return;
        }
        // 查询包含今日日期的闪念文件或内容
        const ideaFiles = dv.pages(`"${ideaPath}"`)
            .where(p => {
                try {
                    if (!p.file) return false;
                    // 检查文件创建日期
                    const createdDate = parseDate(p.file.cday);
                    if (createdDate?.isValid && createdDate.hasSame(targetDateObj, "day")) {
                        return true;
                    }
                    // 检查自定义日期字段
                    const customDate = parseDate(p.created_Date || p.date);
                    if (customDate?.isValid && customDate.hasSame(targetDateObj, "day")) {
                        return true;
                    }
                    return false;
                } catch (error) {
                    console.warn("文件过滤错误:", error);
                    return false;
                }
            });
        // 5. 显示结果
        if (ideaFiles.length > 0) {
            dv.header(4, "💡 今日闪念");
            for (let file of ideaFiles) {
                try {
                    const linkText = file.aliases?.length ? file.aliases[0] : file.file.name;
                    const tags = file.file.tags?.join(" ") || "";
                    dv.paragraph(`- [[${file.file.path}|${linkText}]] ${tags}`);
                } catch (error) {
                    console.warn("显示文件错误:", error);
                }
            }
        } else {
            dv.paragraph("� 今日暂无闪念记录");
        }
    } catch (error) {
        console.error("闪念查询出错:", error);
        dv.paragraph(`⚠️ 查询出错: ${error.message}`);
    }
}

// 使用延迟执行避免初始化时序问题
if (typeof dv !== 'undefined' && dv.current) {
    displayFlashIdeas();
} else {
    setTimeout(() => displayFlashIdeas(), 200);
}
```
