---
Project: 项目：仓储工作经验总结
---
## 目标

- 利用dataview编写查询代码，自动获取当前项目下的所有“输入”、“输出”
- 在创建“输出”、“输出”时自动获取当前项目的页面属性值，并可以根据“鼠标选取”内容自动命名
- 创建核心任务时自动创建链接
- 创建核心任务时自动获取当前项目信息，并添加到文档属性`project`值中
## 行动

- [x] 编写DataviewJs查询代码
	- dv.list()返回查询结果为纯文本列表，需要调整为`dv.list().file.link`
	- 筛选条件需要写在`.file.link` 前，否则查询结果会显示空白
	- `.where()` 表示筛选，内部使用箭头函数编写条件表达式
	- `.where(p => p.project?.includes(project))` 其中`?` 表示筛选文件`project` 不为空，`includes()` 表示包含满足内容为“xxx”
- [x] 测试创建“输入”、“输出”文件，自动添加`Project` 属性及对应项目值
	- `tp.file.selection()` 将鼠标选中内容作为新建文件的名字
	- 使用模板创建文件时，在当前页面出现换行？
		- 删除`<% %>`前后的换行
		- 使用`-%>`表示删除`>`右侧的换行符，`_%>`表示删除`>`右侧以及下方所有的换行；使用`<%*`表示将下方的`%>`之前的内容全部当做JS代码，因此可以实现换行编写代码
			- [[tp插件多行代码书写方式]] 
	- `true` 表示创建的新文件是否打开，经常容易错写成`ture` ；当参数为`true`新建的文件开头重复出现了一次链接
	- 当前创建的文件已存在，系统会自动以“文件名”+“数字”的形式重命名，导致在不知情的状况下创建内容重复的文件？
		- `app.vault.exists(normalizedPath: string, sensitive?: boolean)`判断存在指定路径，返回布尔值；`normalizedPath`表示文件或文件夹的路径（无需从根目录开始写，例如检查文件时：“2-任务/A-项目/项目：仓储工作经验总结.md”），`sensitive`（可选）表示某些文件系统/操作系统不区分大小写，设置为 true 以强制进行区分大小写检查；
		- `tp.file.create_new（“template”,"filename","open_new:false","folder"）`使用指定模板或指定内容创建新文件；`template`用于新文件内容的模板，或字符串形式的文件内容。如果它是要使用的模板，则可以使用 `tp.file.find_tfile(TEMPLATENAME)` 检索它，`filename`新文件的文件名，默认为“Untitled”，`open_new`是否打开新创建的文件，`folder`放置新文件的文件夹，默认为 Obsidian 的默认位置；
		- 注意配合`app.vault.exists()`方法实现目标功能（检查文件）需要创建中间变量以满足`normalizedPath`对应的格式要求
	- 使用 `<% app.metadataCache.getFileCache(tp.config.active_file)?.frontmatter?.Project %>` 获取当前任务下的`project` 值，添加到“输入”、“输出”文件的`project`中
	- 测试“输入”时报错，提示模板调取错误。新建库测试了文件目标路径、笔记模板frontmatter、快速新建笔记代码均未发现异常？不就之前代码还可以正常运行，且已检查发现代码书写错误？
		- 关闭插件并重新打开，恢复正常
	- 新建笔记目标文件存放路径可以简写，并在外部添加双引号。例如："2-任务/A-项目"
- [x] 测试创建“核心任务”，自动添加`Project` 属性及对应项目值
	- 使用`tp.file.name`在创建任务时，frontmatter无法添加对应项目的`Project` 属性值，显示`undefined` ？
		- `app.workspace.getActiveFile().name` 获取当前文件的名称
			- 获取的名称带有`.md` 后缀？
				- 使用`split()` 表示通过搜索模式将字符串分割成一个有序的子串列表，将这些子串放入一个数组，并返回该数组。用法`string.split(separator,limit)` ，参数`separator` 表示分隔符，一般直接用双引号括起来（不需要`\`转译），当直接使用`""`时表示将字符转逐一转化为数组；参数`limit`表示数组中值的显示个数（例如：1表示显示数组中的第一个值，2表示显示数组中前2个值），若省略表示不限制显示个数（即数组中所有值）
				- 表达式修改为：`app.workspace.getActiveFile().name.split(".",1)` 
	- 在“2-任务”文件夹创建文件时，因为其内部有子文件夹、创建文件使用的模板不同，需要设置2个快捷键，整体流程变得更复杂了？
		- 结合`Templeter`插件的脚本功能，但需注意一些问题
			- 需要在新建`Scripts`文件夹，将JS脚本存放其中，并在插件设置中加载
			- `Templeter`加载的JS本质是自定义一个函数（插件未提供且不能在模板文件中直接使用），并在使用模板时调用；函数的固定写法`async function 函数名（）{}`+`module.exports = 函数名`；调用时`tp.user.函数名（tp,tR,参数1的值，参数2的值）` 
			- `async function 函数名（tp,tR,参数1,参数2,……）{}`其中`tp`和`tR`为固定参数，表示将 `tp`和`tR` 对象传递给您的函数，以便能够使用 `Templater` 的所有内部变量/函数；`参数1`或`参数2`理论上可以写很多个（具体未测试）；传入参数的编写顺序必须与JS中定义函数参数的顺序一致
		- `tp.system.suggester((["A-项目", "B-核心任务"], [1, 2]))`表示创建一个提示器，第一个数组表示提示器的显示内容，第二个数组表示与第一个数组显示元素对应的值
		- `tp.system.prompt('请输入项目名称',default_value,true,false)` 创建一个用户输入。`'请输入项目名称'`表示输入框的提示信息；`default_value`表示输入字段的默认值；`ture`表示如果取消则返回报错提醒`cancle prompt`；`ture`表示输入字段将为多行文本区域，默认为`false`
		- `switch(变量){case 值1:表达式1 case 值2: 表达式2}`表示当`变量`的值为`值1`时，从`表达式1`开始向后执行；当`变量`的值为`值2`时，从`表达式2`开始向后执行；`表达式`后跟`break`表示`变量`的值为`值1`时，执行完`表达式1`后停止
		- `tp.file.cursor_append(string)`表示将`string`追加到当前光标位置
	- 如何自动在任务文件中创建”行动“（不可一步操作，即任务），显示链接的别名
		- 使用`[[文件名|别名]]`格式创建链接
		- 在模板中使用`表达式1 ? 表达式1 : 表达式2`语法创建frontmatter，可以实现在「项目」和「任务」中创建任务时，获取不同的值
		- 在JS中使用`${变量名}`串联内容时可以不使用“+”
- [x] 测试创建行动，并自动按规则重名称
	- `tp.file.create_new("templter", "myfilename") ` 将参数`myfilename`以变量形式传入函数
- [ ] 使用内联查询当前项目“输出”、“输入”的文件数量

## 废弃的行动

- 删除任务中孤立的文件
	- 依据当前的任务的创建流程基本不会出现孤立的文件
- 当未用鼠标选择内容时利用弹出的输入提示框进行新任务创建
	- 不利于进行项目解构和任务排布，反而会使工作流更复杂
	- 若想创建孤立的任务，就会不符合“防弹笔记”的思想；因为每一则核心任务均需要有一个或多个明确的成果；同时，从项目维度思考，每一则核心任务也是一个项目，而这些成果对应着项目的目的
- 移植IOTO批量添加、删除指定frontmatter属性功能
	- 每一则笔记的frontmatter属性均由模板自动创建，除非笔记机制规则、框架出现较大变动，否则不会用到这个功能
