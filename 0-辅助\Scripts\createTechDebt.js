/**
 * 技术债创建脚本
 * 功能：
 * 1. 在指定文件夹中根据模板TP-Project-技术债.md创建文件并在新标签页打开
 * 2. 指定文件夹位置"3-过程资产/项目名称/技术债"
 * 3. 项目名称由当前活动文件的路径获取
 * 4. 设置当前日期为created_Date属性的值
 * 5. 弹出对话框让用户填入别名内容
 * 6. 状态默认设置为"待处理"
 */

module.exports = async (tp) => {
  try {
    // 获取当前活动文件
    const activeFile = tp.file.find_tfile(tp.file.title);
    if (!activeFile) {
      new Notice("未找到当前活动文件");
      return;
    }

    const currentPath = activeFile.path;
    console.log("当前文件路径:", currentPath);

    // 从路径中提取项目名称
    // 路径格式：2-项目/个人知识管理系统/2-每日执行/Do-2025-07-01.md
    const pathParts = currentPath.split("/");
    let projectName = null;

    // 查找"2-项目"目录后的项目名称
    for (let i = 0; i < pathParts.length; i++) {
      if (pathParts[i] === "2-项目" && i + 1 < pathParts.length) {
        projectName = pathParts[i + 1];
        break;
      }
    }

    if (!projectName) {
      new Notice(
        "无法从当前文件路径中提取项目名称，请确保文件在'2-项目/项目名称'目录下"
      );
      return;
    }

    console.log("提取的项目名称:", projectName);

    // 构建目标目录路径
    const targetDir = `3-过程资产/${projectName}/技术债`;

    // 检查目标目录是否存在，如果不存在则创建
    const targetFolder = app.vault.getAbstractFileByPath(targetDir);
    if (!targetFolder) {
      await app.vault.createFolder(targetDir);
      console.log("创建目录:", targetDir);
    }

    // 生成文件名：td-YYYYMMDD-序号.md
    const today = new Date();
    const dateStr =
      today.getFullYear().toString() +
      (today.getMonth() + 1).toString().padStart(2, "0") +
      today.getDate().toString().padStart(2, "0");

    // 查找当天已存在的技术债文件，确定序号
    const existingFiles = app.vault
      .getFiles()
      .filter(
        (file) =>
          file.path.startsWith(targetDir) &&
          file.name.startsWith(`td-${dateStr}-`) &&
          file.name.endsWith(".md")
      );

    const nextNumber = existingFiles.length + 1;
    const fileName = `td-${dateStr}-${nextNumber
      .toString()
      .padStart(2, "0")}.md`;
    const filePath = `${targetDir}/${fileName}`;

    console.log("将创建文件:", filePath);

    // 弹出对话框让用户输入别名
    const aliasInput = await tp.system.prompt(
      "请输入技术债的别名：",
      "",
      false
    );

    if (!aliasInput || aliasInput.trim() === "") {
      new Notice("未输入别名，取消创建");
      return;
    }

    const alias = aliasInput.trim();

    // 读取模板文件内容
    const templatePath = "0-辅助/Templater/Notes/TP-Project-技术债.md";
    const templateFile = app.vault.getAbstractFileByPath(templatePath);

    if (!templateFile) {
      new Notice(`模板文件不存在: ${templatePath}`);
      return;
    }

    const templateContent = await app.vault.read(templateFile);

    // 处理模板内容，替换变量
    let content = templateContent;

    // 替换创建日期
    const createdDate = today.toISOString().split("T")[0];
    content = content.replace("created_Date: ", `created_Date: ${createdDate}`);

    // 替换别名
    content = content.replace("aliases: ", `aliases: ["${alias}"]`);

    // 设置状态为"待处理"
    content = content.replace(
      "status: 待处理/计划中/修复中/已解决/暂缓",
      "status: 待处理"
    );

    // 创建新文件
    const newFile = await app.vault.create(filePath, content);
    console.log("文件创建成功:", filePath);

    // 在新标签页中打开新创建的文件
    const leaf = app.workspace.getLeaf(true);
    await leaf.openFile(newFile);

    return newFile;
  } catch (error) {
    console.error("创建技术债文件时出错:", error);
    new Notice(`创建文件时出错: ${error.message}`);
  }
};
