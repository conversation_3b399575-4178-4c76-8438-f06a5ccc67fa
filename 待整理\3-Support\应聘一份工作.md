---
endDate: 2024-09-30
repeat: everyTwodays
---
## 概述

- 动机：生存，需要有钱吃饭；需要偿还贷款
- 目标：找到一份工作，月薪至少在6500元以上
- 愿景：工作内容与仓储相关，不能太“卷”的工作，最好是文职类
## 行动

- [x] 在招聘网站上了解仓储岗位的招聘信息 ⏳ 2024-08-31
	- 工作岗位分类：底层员工（仓库管理员）、基层管理（现场主管、模块主管）、中层管理（仓库经理、负责人）、助力/专员（文员……）
- [x] 初步搜索面试准备相关信息 ⏳ 2024-09-10
	- 面试官的基本考察框架
	- 自我介绍该讲什么？
		- 介绍自己和重点项目
		- 注意交流和沟通方式
	- 项目的复杂程度重要吗？
	- 面试是看眼缘的事吗？
	- 优秀面试者有啥好品质？
		- 对面试的业务有提前了解和准备
		- 沟通过程实事求是
		- 会主动了解业务和自身发展相关问题
		- 懂得倾听
- [x] 寻找面试准备相关资源 ⏳ 2024-09-10
- 课时1：《超级面试官》 
- 制作简历（个人专业、个人优势）
## 闪念

- 如何选择适合自己的工作地点？
- 面试应该准备些什么？具体流程是什么？
## 资料
```dataviewjs
	const links = dv.current().file.outlinks
	const unlinks = links.values.filter((values, index, self) => {
	  return self.findIndex((t) => t.path === values.path) === index
	})
	links.values = unlinks
	dv.list(links)
```
```dataviewjs
	const links = dv.current().file.lists.children.outlinks
	const uniqueLinks = Array.from(new Set(links))//使用Set来删除重复的链接
	dv.list(uniqueLinks)
```