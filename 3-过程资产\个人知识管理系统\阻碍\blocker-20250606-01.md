---
created_Date: 2025-06-06
aliases:
  - 由于无法明确阻碍的处理方法和原则，导致当前工作中断，进而造成本周目标的延误
relation:
  - 整体项目
type: 流程机制
status: 已解决
发生次数: 3
---
# 1. 基础信息

- 发生场景：处理具体阻碍时
- 核心问题：阻碍的具体处理方法是什么？何时应该处理阻碍？处理到什么程度？
- 关键影响：当周目标无法按时完成

# 2. 关键行动

| 时间                  | 行动            | 结果                                                                                                                                                                                   | 状态  | 发现/洞见                                                                             |
| ------------------- | ------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ | --- | --------------------------------------------------------------------------------- |
| 2025-06-06 14:00    | 思考并尝试构建可行性工作流 | （1）记录当前阻碍，并给出尝试方案<br>（2）执行当前尝试方案（IF 尝试方案有效，则记录具体方法；IF 尝试方案无效，则尝试给出其他方案并记录，依次循环；IF 当天无法完成，则将尝试方案转换为[每周计划]中的任务，并提高优先级（尝试安排计划时间））<br>（3）次日按照任务优先级完成相应任务，并循环上述步骤<br>（4）待周维度回顾时，查看并标记阻碍状态 | 失败  | 无论是在执行任务还是其他环节遇到阻碍，都应该立即处理并给出临时解决方案，因为一旦被确认是阻碍，它就一定影响了当前工作流的推进（出现功能型问题），否则就不应该是阻碍 |
| 2025-07-08 10:03:35 | 尝试使用新的框架记录阻碍  | IF“遇到问题”&“影响当前工作推进”，“阻碍”；否则，“闪念“；                                                                                                                                                    | 失败  |                                                                                   |
| 2025-07-10 17:58:20 | 优化阻碍记录判断依据    | IF“遇到问题”&“降低了向目标前进的速度或效率”，“阻碍”；IF “产生疑问”&“为实现短期目标而牺牲长期质量或可维护性的妥协决策或疏漏”，“技术债”；否则，“闪念”或“洞见/发现”                                                                                         | 执行中 |                                                                                   |
# 3. 解决方案

| 日期  | 根因分析 | 行动方案 | 残留风险 | 后续行动 | 备注  |
| --- | ---- | ---- | ---- | ---- | --- |
|     |      |      |      |      |     |
