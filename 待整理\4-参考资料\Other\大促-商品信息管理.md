---
Project: 项目：仓储工作经验总结
---
## 行动

- [x] 筛选异常数据
	- 商品信息管理工作的开展频率期初约定为月维度，但实际工作中从未按照计划执行；一方面因工作精力不足，无暇其他，另一方面是在区域维度仅能筛选出漏维护的字段信息，或极少的数据错误
	- 商品信息经常会影响快件包裹重量（免称重）、商品效期，其他问题基本未出现
	- 除了系统反映出的数据字段遗漏问题，其它只能在现场根据实物测量、检查才能验证系统维护数据的正确性
- [x] 跟踪仓库信息修正进度
	- 仓库按照区域在丰景台导出的错误的商品信息数据，逐一补充或修正异常部分，直至全部修正完成
- [x] 抽查仓库抽检工作
	- 仓库每日对新进SKU进行一定比例抽检，并进行抽检登记，对异常SKU进行信息修正，能明确原因的需要标注具体原因
	- 区域不定期进行仓库自查抽检
	- 仓库实际并未按照要求执行，即使有自查记录，也不清楚仓库实际的执行效果和质量，还等到现场进行实物验证才有效果
	- 整体管理机制运行较差，流程复杂、执行难度大，但当前尚未找到更好的方式方法进行相关工作的管理