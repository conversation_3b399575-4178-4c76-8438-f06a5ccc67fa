# 全局自动技术债工作流使用说明

## 🎯 完全自动化解决方案

这是一个**完全自动化**的技术债工作流解决方案，利用Templater的Startup Templates功能实现**零手动操作**！

### ✨ 核心特性

- **一次配置，永久生效**：配置Startup Templates后完全自动
- **全局监听**：监听所有技术债文件的修改
- **零手动操作**：填写表格保存后自动创建任务
- **智能识别**：只处理状态为"进行中"的行动

## 🚀 使用方法

### 第一步：配置Templater Startup Templates（一次性设置）

1. **打开Templater设置**：设置 → 社区插件 → Templater → 设置
2. **找到Startup Templates**：在设置页面中找到"Startup Templates"部分
3. **添加启动模板**：点击"+"按钮，添加 `0-辅助/Templater/Function/initGlobalTechDebt.md`
4. **保存设置**：确认设置已保存
5. **重启Obsidian**：重启后工作流会自动启用

### 第二步：正常使用（完全自动）

1. **打开技术债文件**：如 `td-20250703-02.md`
2. **填写表格**：在"2. 偿还计划"表格中填写信息
3. **设置状态**：将状态设置为"进行中"
4. **保存文件**：Ctrl+S 保存文件
5. **自动完成**：1秒后自动在Plan文件中创建任务

## 📋 工作流程对比

### ✅ 自动化流程
```
1. 配置Templater Startup Templates（一次性设置）
2. 填写技术债表格
3. 保存文件
4. 自动创建任务  ← 完全自动
```

## 🔧 技术实现

### 全局监听机制

```javascript
// 监听所有文件修改
vault.on('modify', globalListener);

// 只处理技术债文件
if (file.path.includes("技术债") && file.path.match(/td-\d{8}-\d{2}\.md$/)) {
    // 自动处理
    await processTechDebtFile(file);
}
```

### 智能文件识别

- **路径匹配**：`3-过程资产/项目名称/技术债/td-YYYYMMDD-XX.md`
- **表格解析**：自动识别"2. 偿还计划"表格
- **状态检测**：只处理状态为"进行中"的行动

### 自动任务创建

- **项目识别**：从文件路径自动提取项目名称
- **Plan文件定位**：自动计算当前周数并找到对应Plan文件
- **任务格式**：`- [ ] ⟦技术债⟧行动内容`
- **重复检查**：避免创建相同任务

## 📁 文件结构

```
0-辅助/
├── Scripts/
│   ├── globalTechDebtWorkflow.js       # 全局工作流主脚本
│   └── 全局自动技术债工作流使用说明.md    # 本说明文件
└── Templater/
    └── Function/
        └── initGlobalTechDebt.md       # 一次性初始化模板
```

## 🎛️ 管理功能

### 调试功能

如需调试，可在浏览器控制台执行：
```javascript
// 停止工作流
window.stopGlobalTechDebtWorkflow();

// 检查运行状态
console.log("工作流状态:", window.globalTechDebtWorkflowInitialized);
```

## ⚠️ 注意事项

### 重要提醒

1. **一次性配置**：配置Startup Templates后，无需再手动操作
2. **自动启动**：每次Obsidian启动时会自动启用工作流
3. **表格格式**：确保技术债文件包含正确的表格格式

### 表格格式要求

```markdown
# 2. 偿还计划

| 创建日期 | 行动 | 关键进展/发现 | 状态 |
|---------|------|-------------|------|
| 2025-07-03 | 优化性能 | 完成初步测试 | 进行中 |
```

### 文件路径要求

- 技术债文件：`3-过程资产/项目名称/技术债/td-YYYYMMDD-XX.md`
- Plan文件：`2-项目/项目名称/1-每周规划/Plan-YYYY-WKXX.md`

## 🐛 故障排除

### 常见问题

1. **任务没有自动创建**
   - 检查是否已配置Startup Templates
   - 确认表格格式正确，状态为"进行中"
   - 查看浏览器控制台是否有错误信息

2. **重启后失效**
   - 检查Startup Templates配置是否正确
   - 确认模板文件路径无误

3. **创建了重复任务**
   - 脚本会自动检查重复，正常情况下不会重复创建
   - 如果出现重复，检查任务文本是否完全一致

### 调试方法

1. **查看控制台日志**：F12 打开开发者工具查看详细日志
2. **检查初始化状态**：确认 `window.globalTechDebtWorkflowInitialized` 为 `true`
3. **测试文件修改**：保存技术债文件后查看控制台输出

## 📊 使用示例

### 完整使用流程

1. **配置Startup Templates**（一次性设置）：
   ```
   Templater设置 → Startup Templates → 添加 initGlobalTechDebt.md
   重启Obsidian → 自动启用工作流
   ```

2. **日常使用**：
   ```
   编辑：td-20250703-02.md
   填写：| 2025-07-03 | 优化日志性能 | 测试完成 | 进行中 |
   保存：Ctrl+S
   结果：自动在Plan-2025-WK27.md中创建任务
   ```

3. **查看结果**：
   ```
   Plan文件新增：- [ ] ⟦技术债⟧优化日志性能
   ```

## 🎉 优势总结

- **🚀 零手动操作**：填写表格后完全自动
- **🎯 一次设置**：只需初始化一次
- **🔄 全局监听**：监听所有技术债文件
- **🛡️ 智能防重**：自动避免重复任务
- **📱 即时反馈**：保存后立即创建任务
- **🔧 易于调试**：提供完整的控制台日志

## 🎉 总结

现在您只需要：
1. **配置一次Startup Templates**（一次性设置）
2. **正常填写技术债表格**
3. **保存文件**
4. **任务自动创建**！

完全实现了您要求的"去掉手动执行脚本的动作"！利用Templater的Startup Templates功能，实现了真正的零手动操作自动化工作流。🎉
