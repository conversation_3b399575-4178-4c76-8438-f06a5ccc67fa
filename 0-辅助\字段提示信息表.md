## KR进度描述 ^b44aa3

- 核心动作​​：阐述交付物（Output）对关键结果（KR）的价值影响
- KR类型
	- 探索性KR（难以量化）
	- 标准KR（「行动路径」+「量化结果」）
- 关键问题​​
	- 探索性KR（难以量化）
		- 我们需要通过本周/本迭代的工作回答什么关键问题？（识别关键验证点）
		- 需要获取哪些信息才能推进决策？（识别关键决策点）
		- 通过[具体活动]，我们确认了/发现了/排除了 [具体发现、验证的假设、消除的选项、识别的关键风险/机会]（关键认知/验证）
		- 基于以上认知，我们决定 [具体下一步行动/决策/优先级调整]，这将推动KR向 [下一步目标] 迈进（决策支撑/方向明确）
	- 标准KR（「行动路径」+「量化结果」）
		- 这些交付物对KR指标的具体影响

## 周目标描述： ^f37322

- 目标1（[KR1]）
- 合格目标 = 核心价值 + 可衡量状态/可量化结果 + 时间线
- 回答“为什么？”

## 闪念描述：[触发场景] + [核心方案] + [价值]​​ ^ec14e2

- 问题探索：为什么、原因、根因、分析、调查、不清楚（在 [环境] 下[疑惑]？）
- 风险预警：风险、问题、警告、可能、担心、阻碍（在 [环境] 下发生 [现象]导致 [异常影响] ）
- 流程优化：流程、步骤、效率、慢、麻烦、自动化、流程改造、重组现有元素关系（具体场景中是否包含流程）
- 需求响应：客户、用户、反馈、想要、需要、请求、Bug
- 创新方案：想法、试试、如果、新方法、创意、假设

## 验收标准描述 ^19887e

- 完成[具体动作]，使[指标]从[当前值]达到[目标值]（数据来源：[xxx系统/报告]）
- 交付[具体成果物]，并通过[验证方式]/完成[关键行动]，产生[可追溯证据]
- 当关键细节不明确时，采用「负面清单+关键行为」验证的写法

| 类型    | 目标       | 负面清单特征            | 关键行为验证（必须观测到）                                              |
| ----- | -------- | ----------------- | ---------------------------------------------------------- |
| 流程防卡型 | 确保动作流连续  | 禁止导致流程中断/倒退/停滞的行为 | 1、用户按预设顺序完成所有环节  <br>2、无超过阈值（如30秒）的操作迟疑 <br>3、未出现步骤跳过或回退   |
| 功能防漏型 | 保障核心功能实现 | 禁止关键功能失效          | 1、核心功能输出物存在（如：偏差报告）<br>2、功能输出驱动了决策（如：改进措施进入执行）             |
| 认知减负型 | 降低用户理解成本 | 禁止引发困惑/疑问/误操作     | 1、用户首次操作时求助次数≤1次<br>2、无执行与设计意图相悖的行为<br>3、完成后的认知负荷评分≤2（5分制） |
| 价值闭环型 | 强制实现价值传递 | 禁止价值断链（如：分析未驱动行动） | 1、输出物被下游环节使用（如：复盘问题纳入迭代计划）<br>2、产生可验证的业务影响（如：延迟率下降10%）     |
- 渐进式构建

## 技术债 ^7ffbc6

- 技术债：在具体开发过程中，为实现短期目标而牺牲长期质量或可维护性的妥协决策或疏漏

## 成果验收 ^59b696

- 成果验收：交付物清点和确认
	- 核心动作：客观记录本周实际完成的具体产出
	- 关键问题
		- 计划中的功能/任务是否完成？
		- 交付物是否达到质量验收标准？