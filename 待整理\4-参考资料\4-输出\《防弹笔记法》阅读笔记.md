---
Project:
  - 项目：仓储工作经验总结
---
## 内容大纲

- 创建核心任务笔记
	- 集中处理：当下把不同工作管道的碎片集中在一个地方，当下把数据写成自己理解、需要的内容，并放入需要的位置
		- 2个追问：
			- 我为什么要收集这个资料？这是要完成哪一个任务？关于哪一个知识主题？要解决哪一个问题？
			- 我打算之后如何使用这个数据？
		- 清空大脑的7个技巧：
			- 捕捉到一个任务到一则笔记（经过「任务成果」、「我的行动」转化到核心任务笔记或项目笔记）
				- 误区识别
					- 误把事件当作任务（一次会议、一日清单）
					- 误把行动当作任务（真正的任务单位应该是「要完成某个具体有效益的成果」）
					- 误把工作流程当作任务成果
					- 没有为自己主动设计任务成果
			- 捕捉想做、要做的下一步行动到任务笔记
				- 要完成这个任务，我拆解出来要做的下一步行动
				- 跟这个任务有关，但临时插入、变动意外产生的下一步行动
				- 跟这个任务有关，还没确定是否要做，未来可能需要的下一步行动
			- 把数据整理到任务笔记中需要的行动后
				- 方法
					- 根据数据类型整理（例如会议记录、客户资料个别分类等等）
					- 根据获得资料的时间整理（反正就是丢上去，节省时间）
					- 根据任务的下一步行动，决定数据哪个流程步骤要用，就这样来整理
				- 原则
					- 一个任务的行动、数据、碎片，都应该在一则任务笔记中，而非分散成很多笔记
					- 数据不是根据数据逻辑整理，而是根据一个任务的执行逻辑整理
					- 完成任务成果，才有意义。而整理笔记或数据本身，则通常没有意义
					- 根据任务的下一步行动，决定数据哪个流程步骤要用，就这样来整理
			- 随时捕捉出现的想法到任务笔记
				- 类型
					- 任务目标的设想
					- 任务发现的问题
					- 觉得好像可以这样做，但是还没想清楚的想法
				- 误区（错误）
					- 不要乱捕捉，也不需要做额外整理，就是找到所属的任务笔记，捕捉进去即可
					- 捕捉时，就是把目前新出现的想法捕捉进去（没有出现的就不用硬去想）
					- 只要随时捕捉与累积，慢慢的一个有效的目标自然就会更容易出现
					- 捕捉完，就立刻离开，继续回到工作
			- 用最简洁的笔记列表格式
				- 用大纲列表（树状列表、项目列表）列出笔记的目标、问题、阶段进度、行动列表的分层结构
				- 笔记的内容顺序，就是任务的行动执行顺序。所有信息的整理顺序，就是跟着如何使用信息的行动顺序排序
			- 如何减少整理又能确实找到？搜寻
				- 怎么做？
					- 当有任何噪声要捕捉时，「搜寻任务关键词」，找到所属的任务笔记放进去
					- 当出现新的意外变动行动时，「搜寻任务关键词」，找到所属的任务笔记放进去
					- 当出现一些新的想法时，「搜寻任务关键词」，找到所属的任务笔记放进去
				- 类别
					- 任务的专有名词：任务、项目的名称关键词
					- 利害关系人的专有名词：客户、客户公司、老板主管的名称
			- 捕捉自己正在执行的内容：跟着任务执行一起记录，把执行过程中产生的新行动、新想法、新问题，也都随手补充到任务笔记中
	- 三个任务成果问题：为谁为何而做？如何量化成果？有何阻碍限制？
		- 为谁为何而做：追问一下这件事情是要为谁满足什么目的？（这不只是时间管理的关键步骤，也是写笔记的关键步骤。笔记最终也依然是要为某些人创造某些价值目的）
		- 如何量化成果：时间成果、数量成果、特殊成果
		- 有何阻碍限制：
			- 准备的阻碍：例如报告的所有数据都在我手上吗？如果不是，我要跟谁要？  
			- 执行的阻碍：如果设计不出新的营销计划，有没有备案？  
			- 结案的阻碍：有没有可能老板忽然要我们口头报告？或是老板突然提前要这份数据？
	- 三个行动拆解技巧：拆解下一步行动、插入可测试行动列、必要的关键行动
- 创建防弹笔记系统
	- 架构系统的方法：一个任务，一则笔记。一个项目，一则笔记（一个任务的所有碎片，都集中在一则任务笔记中管理。也代表一个大型项目或目标的所有细节，都链接到一则项目目录笔记中管理）
	- 变动和更新
		- 有些事情决定不做，也可以画一条删除线代表事情决定不做
		- 调整顺序到后面的封存大纲
		- 更新工作上的大项目、小任务流程式链接（每一次都主动更新到他们要解决的项目、任务笔记中）
		- 更新生活中的经验笔记（生活不一定是零散的杂事，可能只是我们没有为自己的生活找到目标与任务）
		- 我更有意识的关注已经发生在自己生活、工作上的事情。（所以才能更新新内容到旧笔记中）
		- 我经过思考才写下笔记，因为要更新、连结、统整，表示内容一定经过思考才做得到
		- 我开始从目标的角度去解决问题，而不是从资料整理的角度以为能解决问题。
	- 补充
		- 在笔记大纲中，可以随时调整，只要复制贴上，插入到大纲需要的位置，或是前后移动即可
		- 这样反而可以保持变动的弹性，随时变动都能立即修改
		- 而大纲顺序是行动顺序，也就已经是项目需要的执行顺序了
	- 复盘：真正要养成的不是覆盘的习惯，而是「有意识的笔记」习惯，意思是在执行任务时做好下面几种内容的笔记
		- 我做了什么行动？
		- 过程当中遭遇哪些问题点？
		- 一边做一边想到的改进想法
	- 重复任务：在任务知识笔记中加入下一次要试试看的新想法、新步骤
	- 每日行动列表（每周一次的视野）：需要先看到项目（核心任务笔记、目标笔记）的视野
		- 对我个人来说最有价值的目标：可能是生活、斜杠上的项目，也可能是工作项目，但对自己最有价值，或许不是公司要求的，但需要利用自己个人的时间推进
		- 需要推进进度的要事：工作上某些有价值且长期的任务、项目（可以是一周后要完成的重要任务，或是几个月后要完成的项目），今天应该要推进的行动
		- 其他琐事：剩下其他要做的事情，都归类到其他琐事（例如临时交办的小事、次要的意外处理）
	- 暂存工具或流程（现实环境不一定有时间操作、思考）
		- 步䠫：
			- 每周要列下周子弹清单时就会回顾、理清一下这几天的收集箱
			- 不用要求自己一定要完全清空，因为人难免会收集一些多余的数据，有些数据在「（暂存）收集箱」放久了都没有归档，其实是提醒我这些是多余资料，就在不想再看到他们的时候一次删除他们即可
		- 处理方法：
			- 合并成正式的任务笔记：发现有几则暂存笔记其实都在想同一个问题，就合并在一起
			- 剪贴到原有的任务笔记：发现这则暂存笔记，跟某几则笔记相关，但又有不同，不要担心，就拆开内容放入不同相关笔记中
			- 不要连结：因为我们现在面对的只是暂存笔记，让这样的笔记去做链接，会变得很零散
	- 链接
		- 处理方法：
			- 数据、档案，链接在任务笔记需要的行动后（「主从次序」、「某个行动要参考经验」，都是有明确的「使用需求」，是在某个步骤确实需要链接到另外一则任务，才建立这个步骤后的明确连结）
			- 用链接整理不同任务笔记的执行顺序
			- 用链接建立项目目标笔记的目录、入口
			- 随手连结，节省设计连结的时间（不要刻意花时间整理连结）
		- 类别：
			- 这个知识主题（任务项目），是来自哪个上一层知识主题（任务项目）的子题目、子问题、子任务
			- 这个知识主题（任务项目），到了哪一个段落还有未解问题、未完成进度，要延伸到下一层知识子题目（任务项目）继续拆解、执行
			- 在这个段落、行动，需要参考另一个知识主题，作为辅助思考的参照。或是需要参考另一个任务项目笔记，作为经验参照
	- 行动情景（价值判断、行动判断）
		- 项目笔记
			- 每日推进：通常大约会有 20~30 则笔记加上这个卷标，代表我在目前人生当中随时都应该推进的几个目标
			- 企划酝酿：通常大约会有 60~80 则笔记加上这个卷标，代表我正在酝酿中的各种企划、项目，但还没有排上时程真正开始推进
			- 定期追踪：已经完成，但偶尔要追踪一下的某些项目、任务
		- 写作
			- 撰写完成：进度超过 50% ，并且想法已经抵定，就是要花时间最后写完的文章。当我有一些比较正式的专注时间时，我会打开「撰写完成」标签，推进这些即将写完的文章进度
			- 测试研究：需要用一些数字装置测试的工具，需要实践一下的方法。当我有一些零碎时间，但脑筋转不动时，我会打开「测试研究」标签，推进这些需要先测试看看（动动手就好，不一定要动脑）的文章进度。测试到一定阶段，这些文章就可以转换成「撰写完成」标签
			- 思考动脑：需要想清楚方法论，需要解析问题的文章题目。当我有一些零碎时间，而且脑筋可以转动，我会打开「思考动脑」标签，推进这些需要动动脑的题目。解出一定程度答案，这些文章就可以转成「撰写完成」标签
		- 生活
			- 两人时间：有些事情是可以跟另一半一起完成的，这样的笔记我就加上这个标签，当出现适合的空档时，打开这个标签，就可以选择想要推进的任务
			- 一人空闲：有些兴趣只有我自己有兴趣，例如我自己想读的书，我就加上这个标签，当出现我 一个人的空档时（例如上完课回程的高铁上），我就打开这个标签，选择一件事情来推进
			- 想和孩子一起做：有些娱乐、学习是可以和孩子一起做的，我就把这样的笔记加上这个卷标，现在你应该知道我什么时候会打开了吧？
	- 动态演化笔记的5种类型
		- 暂时的笔记（「暂时的笔记」经过处理后，可能会变成两种笔记：「知识与经验笔记」、「任务笔记」）
		- 知识与经验笔记
			- 一种是「暂时的笔记」经过处理后，也完成初步学习行动，以后其他任务会有参考价值，这时候就会变成「知识笔记」
			- 一种是「核心任务笔记」完成后，不需要再采取行动，但以后会出现类似、重复任务可再使用，会变成「经验笔记」
		- 核心任务笔记
		- 永久型任务笔记
		- 项目笔记

## 结构

- 项目
	- 核心任务笔记
		- 行动
			- 思考
			- 问题
			- 灵感
			- 数据
	- 属性
		- 更新、补充、变动
		- 每日行动安排
		- 情景判断
	- 暂时笔记

## 机制&原则

- 自下而上创建笔记
- 「暂时的笔记」经过处理后，可能会变成两种笔记：「知识与经验笔记」（思考、问题、灵感、数据）、「任务笔记」



