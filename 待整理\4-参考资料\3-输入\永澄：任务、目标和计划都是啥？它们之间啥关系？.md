---
Project:
  - 项目：仓储工作经验总结
---
## Part 1

但凡要给自己定目标，考虑时间管理、效能提升的人，都绕不开这三个概念：**任务、目标和计划**。

弄不清楚三者关系并且各种混用的大有人在，比如说：周计划和周任务、月目标和月计划、年目标和年计划的区别是什么？你是否能说清楚？如果说不清楚，还想要确保目标能够达成，那简直是太难啦！

老妈子附身，必须啰嗦几句：我在以往的文章中反复强调过概念的价值。概念是对具体事物的抽象，概念有明确的边界、原则、方法、策略，如果概念不清楚，很多方法就不会适用。比如说目标可以使用SMART原则，但是计划就不能啊，**概念不清很可能会导致从源头犯错，目标还怎么实现呢**？

我想通过本文把这三个概念是什么、彼此的关系说清楚。开始前，需要嘱咐一句：本文用逻辑来分析概念，每个概念都是彼此包含的关系，你必须静心阅读，给自己留足一小时的时间，就当是上了一节课吧。

我敢向你保证，你投入的一小时，会换来成千上万倍的收益！说一个最简单的收益，有本书，奇难无比，每次看都有新收获——《搞定1——无压工作的艺术》，它其中有张图：

![](https://pic1.zhimg.com/v2-74c0d2ff7ea8714cb400774613e8ce80_b.png)

你不仅可以轻松看懂这张图，可以轻松看懂《搞定1》这本书，还可以打好目标管理、时间管理、行动管理的所有基础！)
## Part2

在正式开始之前，我要先介绍一个前置概念：**行动（Action）**。

![](https://pic4.zhimg.com/v2-ed2bc0f640d7ce511cc724097d0e8603_b.png)

**行动，指的是通过一步操作即可完成的、不可细分的最小事项/活动**。比如说：给小王打电话、发传真、到超市买东西、洗碗、整理10家公司的数据、给老板汇报项目进展等。

概念中“一步操作”、“不可细分”对不同人而言，标准是不一致的。这里需要特别说明：所谓的“一步操作”，由于每个人的能力不同，“一步”的长度是不同的。就拿使用软件来说吧，比如说“使用word写一个会议通知”这个事项，对我而言（拥有五年材料经验的前政府机关人员），这就是一个行动，可以一次性完成；而对于一个刚出生的孩子，她可能需要掌握鼠标、敲击键盘、人类语言基础、写通知的基本格式、遣词造句才能实现。所以，对我来说的行动，对于孩子来说可能是需要十几年才能实现的大目标。再举个例子感受下，王健林说：“我们要先实现一个小目标，先赚一个亿”，他在说这句话的时候，目标已经实现了，可是对于很多人来说，这是一辈子都无法实现的事情。

**只有行动才可以被执行**，这是任务管理中的基本原理。任何任务、目标、计划都需要转化成行动才能被执行。由于“不可细分”一词对于不同人、在不同阶段、不同情绪状态下，都是不一样的，所以，“行动”这个概念的用法，通常有两种：

• **规范描述**：当你提到“行动”时，**其中必须包含动词**。如果说你的行动是“8杯水”、“超市”、“今天的天好美”，这都不是行动，因为它们不可被执行，需要改成：“装满8杯水”、“去超市接人”、“快拍照，今天的天好美”。

> [!question] 思考
> 若当前行动不可一步操作，那显然就是一个任务，那这个任务是一则核心任务笔记吗？
> 如果「项目1」有「任务A」、「任务B」两个任务，其中「任务A」拆分的多个行动中，有一个或多个不可一步操作，那这个“行动”（任务）在笔记中应该如何呈现？需要单独新建一个新的核心任务吗？
> >方式一：将“行动”拆分出真正的行动，并将其展开至「任务A」的其他行动中
> >方式二：将“行动”转化为一则核心任务笔记，并在「项目1」中将其作为「任务A」的子任务放置；同时，把这个“行动”链接到「任务A」的其他行动中，让其排布在正确次序上，保持所有行动的结构和逻辑
> >方式三：「任务A」页面中直接将“行动”拆分成真正的行动，并对这些内容进行降级，变成这个行动下的“子行动”

• **落实任务**：用话术的方式来帮助自己思考某任务是否可以落地，话术为：“**现在已经分解到行动的层级了么？可以执行了么？**”比如老板要求拿出一个市场策划案，这是一个大任务（关于任务的概念，我们后续说），如果落地执行必须分解成行动。在分解完成后，就可以使用这句话术向自己提问，每个分解出的小任务是否可以被执行？

行动是一个基础概念，它表示一类事项，可以直接行动的事项。**而一件事情对自己来说到底是不是行动，不是别人说了算的，必须以自己的能力为标准判断。如果自己的能力储备不足，并不能通过直接执行得到结果（概念中的“即可完成”），这个事件就不是行动，你需要持续分解，直到可以被执行。**

我有句忠告：一件事是否是行动，你需要**真诚地对待自己**，不需要假装自己很厉害，把所有事情都看的太简单，也不需要严格分解所有的事项，那样管理成本就太高了。让这件事处于一个合理的管理成本区间就够了。也就是说，管理成本区间决定着一件事对自己来说究竟是不是行动。

## Part3

有了行动（最小事项/活动）这个基本概念之后，就可以来看看任务（Task）是什么了。

![](https://pic4.zhimg.com/v2-dbe7bd733489a5304f88467b26a268a7_b.png)

**任务是由若干行动有机组合而形成的事件/活动集合，任务可以通过一定方式分解成行动**。通过上图可以看得很清楚，我们可以将其理解成：只要不能一步完成的事项/活动都是任务。比如说：开发一款产品、做一次品牌营销事件策划、举办一期训练营、完成一次房产投资交易、翻新家庭装修等等。

但是，任务的概念也不仅仅那么简单，来看看概念中的用词：

• **若干行动**。“若干”指不定量，一个或者多个。所以，这里隐含着一点：存在着由“一个行动”构成的任务，我要表达的是：行动是一种特殊任务。为了更好地区分，未来再说提到任务的时候，指的是必须由两个或者两个以上行动组成的事件集合。

• **有机组合的集合**。不是说把行动放在一起就是任务了，任务有其整体性。我们用最简单的集合来理解：把两条行动“吃饭”、“买票”放在一起组成一个任务“去餐厅吃饭”，那这个任务本身也是可以被理解的。如果行动组合成的任务不能被理解，那就不叫任务，比如说“一边在家吃饭，一边在电影院排队买票”就超出认知范围，这就不是“有机”组合。也可以这么说，**任务是由行动构成的有机整体**。正如上图展示的那样，组合在一起。

• **一定方式分解**。任务是可以被分解成行动的，但是必须要通过一定方式来分解，不同人掌握的“方式”不同，把任务分解成行动（就是所谓的“任务分解”）的过程也是不同的，所以，即便同样的任务，不同人的处理方式也是不同的。另外，这一条还说明，如果你不具备“方式”，很可能有些任务是无法分解的，比如说“先赚一个亿”，不能分解就不能执行，就只能卡在那。**引申一下：如果你发现自己卡住了，不要抱怨/低落/挫败，而是要去找到有效的“方式”**。

我再把上图重新改动一下，可能更容易理解：

![](https://pic4.zhimg.com/v2-546e13f3c0182fcb554b35b86e40e067_b.png)

总结一下：从整体上看，任务是一个整体，打开它的表面，它还是若干行动构成的有机集合，这个集合可以通过某种方式分解成一个个的行动。**之所以有任务，就是因为通过实施任务，可以得到结果，而那个结果是我们真正想要的（目的：Purpose），任务只是达成结果的执行载体**。

![](https://pic1.zhimg.com/v2-cb8c0db1a15ef678a2782d58464cb6b4_b.png)

目标是什么，它跟任务和行动之间又有什么关系呢？

目标管理之所以难学，就是因为“目标”是个多义词，它包含非常丰富的内涵。尝试着来举几个例子：

• 你人生的目标是什么呢？

• 这个文档的写作目标是什么？

• 你的月目标怎么定的呢？

• 我们推进这项工程落地，一定要按照工期达成目标。

• 我们团队这次的目标就一个：赚到1000万。

你看，在目标管理中会涉及到个人目标、组织目标，只说个人目标又有领域目标、时段目标、项目目标等，只看时段目标就包括：日度目标、周度、月度、半年度、年度、3年、5年……目标。很麻烦的是，虽然它们都叫目标，但是随着时间长度的不同，处理它们的方法是完全不同的。比如说：“叫外卖的目标”、“人生目标”，这两者的管理方法是完全不同的。

为了降低管理成本，我们从复杂中找到核心规律来重新认识目标，让我们从“**任务和目标的关系**”来切入，帮忙理解上述的复杂目标体系。（括号内文字送给了解我三观体系的伙伴：你会发现，目标管理非常复杂，那说明它是在现实世界的投影，它的本质应该非常简单，在真实世界中有极简规律对应，而任务和目标的关系，就是这个极简规律：目标的复杂程度，是由任务决定的，再具体点说，是由任务中行动的多少决定的，行动越多目标越复杂）

在第三部分讲到任务时，最后总结了这样的一段话：**之所以有任务，就是因为通过实施任务，可以得到结果，而那个结果是我们真正想要的（目的：Purpose），任务只是达成结果的执行载体**。我来画张图帮你理解这句话：

![](https://pic3.zhimg.com/v2-afbd478faa7d33cb457009062e18490a_b.png)

一个人想要做某件事，必然有动机驱动，必然要寻求某一个目的（比如说逃避损失、达成使命、维护关系、满足占有欲……），为此，他必须要通过执行任务来取得结果来实现目的。这就是我常说的**PORT模型**（在公众号输入关键词：PORT，获得模型的具体解释）中的三个关键词——Purpose-目的、Result-结果、Task-任务的逻辑框架图。

**那PORT模型中的O：gOal-目标在哪里呢**？说了半天任务和目标的关系，但并没有谈到目标啊！

先来定义目标是什么，**目标，目是指眼睛，标是指标准，目标就是视野范围内可以看到的标准**。目标管理中有个著名的SMART原则，这是目标有效性分析模型，判断一个目标是否有效，它表示：明确的、可衡量的、可达成的、具有相关性、有时间限制。结合概念理解：“眼睛中可以看到的”那必须要明确、可以达成（如果无法达成，就看不到了）、有时间限制；“看到的标准”指的就是可衡量；你之所以愿意去看、去追求这个标准，是因为具有价值相关性。

继续问：任务和目标是什么关系呢？答案来了：**目标是任务的固有属性**。啥啥啥？什么叫固有属性？来举个例子吧，一个人（X）的身高（Y1）、体重（Y2）、兴趣爱好（Y3）等，身高、体重、兴趣爱好就是一个人的固有属性。我们抽象来看，形成一个套路：Yn是X的固有属性，X是对象，Yn是属性，用属性可以描述对象。

![](https://pic1.zhimg.com/v2-6631a2772be889803d339cfe34d09ec8_b.png)

正如一个人的兴趣爱好有不同，一个任务的目标属性也可能有不同的值。我们从最小任务——行动的目标来看：把一小碗面条吃完（行动）的目标是什么呢？可能是把面吃完把汤剩下，也可能把面和汤都吃完，还可能吃完后再把碗舔干净。

这里要说明的是这样一个道理：**即便执行同一件任务，因为**目的（Purpose）不同，**所需要达成的标准（目标）也是不同的**。所以，要多训练自己对应“目的-任务-目标”的能力，才能对一个任务的目标有更好的把握。

好了，这个部分说得比较复杂，我总结这个部分的5个关键要点：

• 目标是眼睛中可以看到的标准，用这个定义可以解释SMART原则；

• 目标很复杂，为降低管理成本，要从一个点切入理解，这个点是“任务和目标的关系”；

• 目标是任务的属性，任务和目标的关系是：任务是对象、目标是属性；

• 任务的目标属性有不同的值，任务之所以体现出不同的目标属性，是由执行任务的人背后的目的所决定的，目的不同，即便同一件任务，目标也不同。

• PORT模型中的O-目标，其实蕴含在任务之中，根据目的不同，目标也不同。这就是“做任何事前必须要澄清目的”的底层逻辑。

此外，这个部分还隐含了个知识点，我简单说下，未来有机会用一篇新文章详细展开。一个任务的目标是否复杂，取决于任务大小（包含行动的多少），**任务越大目标越复杂**。原因：由多个行动有机组成的任务既具有整体的任务目标属性，也具有分解后的每个行动的目标属性（每个行动都拥有目标属性），所以，**如果包含行动越多的任务，目标管理就会越复杂**。

只有一个人能力水平很高（执行过的任务多，尤其是复杂任务多），他才可以对一个复杂任务的整体目标进行判断，才能提出类似“一年赚取500万”、“GPD增长8%”、“成为全国著名的目标管理专家”等整体目标，还知道如何通过分解任务达成这个目标。正是由于反复多次刻意训练，对行动、小任务的目的-目标澄清有着十足的表征，才拥有了提出整体目标的能力。如果没有充分训练，虽然可以提出整体性目标，但是该整体目标是否可以实现是不能有效把握的，这就是为什么很多人提不出复杂目标（比如：中长期目标，或者直接给出下个月的目标或者自己未来工作发展目标），或者提出目标也是白提的根本原因。

给初学者一个建议：任务和目标的关系，必须是先有目的、后有任务、再有目标，强烈建议按照细分流程刻意练习的，只有训练多了，才能直接提出整体目标，没有金刚钻别揽瓷器活，否则，目标失败会一直陪伴着你。

![](https://pic2.zhimg.com/v2-289ee9b003fc497021023b0829531c05_b.png)

说完目标是什么了，该来谈谈计划了！直接来定义，**计划是为了达成任务的整体目标而对任务分解出的行动进行排序的方式和结果**。

复杂吧？我们把定义分解一下：

• 为什么要有计划？为了实现任务的整体目标。

• 计划是什么？是方法，作为动词使用，计划（安排）某某事情；也是结果，作为名词使用，做出一份计划。当它是动词使用时，主要对行动进行排序，排序之后的结果，就是名词形式。

• 排序是什么意思？通常来说要对行动的先后关系、重要程度、执行方式进行考虑，最终排列行动的串并行执行关系，就是排序。

还是看不明白，依然画图来看：

![](https://pic4.zhimg.com/v2-1621ab0b51cbf7674008d9de986234af_b.png)

因为咱们今天谈概念，所以不展开说过多方法，比如说如何使用WBS的方式来分解任务变成行动，也不说如何对行动进行排序，只要能够理解清楚概念就好了。 计划这个概念包括“排序”和“排序后的结果”这两大部分。

有没有发现，计划这个概念很简单，它和任务的关系也变得非常清楚了？因为我们已经通过了最难理解的“任务和目标的关系”，再来看“任务和计划”就变得容易多了。

来，我们考试一下看看：

• 日计划是 ？

• 月目标和月计划是什么关系？

• 目标和计划之间是什么关系？

• 什么样的人可以制定年计划？

• 当拥有什么能力之后，制定年度目标能更靠谱一些？

![](https://pic1.zhimg.com/v2-47fe01018330e83150f268bd3a4208f0_b.png)

来吧，综合总结本文，我到底说了什么，看图：

![](https://pic2.zhimg.com/v2-7ba7508f7b19f5b8da65413f79e5f2f1_b.png)

每个人都有自己想要的（想得到-期待导向、想逃避-问题导向），但是水平不同，效能不同：水平低的就会瞎做事情，以为自己可以得到想要的结果，这种无意识、无方法的人效能通常很低。

来看看高水平的人——他是既知道自己要什么、还知道怎么去得到的人（做对的事，把事做对），他的做法是：先去分析如果想要实现目的，需要得到哪些结果；通过结果推导出任务以及任务所需要达成的目标；之后把任务分解至可以执行的行动；再对行动进行排序形成计划；按照计划去执行最终达成目的。

这是一个完整的流程，具体执行的时候要根据目的和任务的大小来选择相应的标准，哪个环节要做成什么样的，哪个环节可以省略，这些标准的形成来自于刻意训练。**这是所有自我效能提升类学科（目标、时间、任务、行动管理）的底层逻辑**，**对于上图标准的把握、对流程的应用能力，直接体现出一个人的自我管理能力，也决定了一个人的效能水平**。