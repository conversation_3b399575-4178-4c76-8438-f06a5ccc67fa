- [x] 梳理、归类数据类文件资料 ^8105ad
	- 方便后续在梳理工作经验时，可以快速定位，并链接
- [x] 尝试梳理数据、资料类知识
	- 不知道选择什么样的方式进行整理？
		- 将现有的存档资料进行归类，以【项目分类清单】方式进行整理
			- 在项目笔记中将数据资料、文件以链接方式，罗列在笔记最下方
		- 现有资料是以附件的形式存放，还是转化为markdown进行存放，还是直接对附件进行整理存放在网盘？
			- 在实际工作中，可以将存储资料文件夹放在obsidian库中，或者使用软连接将其映射到obsidian库中，当做代替文件夹管理的另一种方式管理文件，这样会更高效。在自己的电脑中部署时，因为不常用，所以尽量少的将文件转为markdown存储（图片、表格不友好），直接放置一个网盘链接即可
	- 在进行「核心任务笔记」编写时，通常需要基础的专业知识的支撑才能完成，在此时进行对应的数据资料的整理或编写并将「数据」对应链接到某个行动下会让笔记结构和逻辑更加顺畅，；若当前「数据」对应的内容无法转化为「核心任务笔记」，可以在改内容所属的项目笔记中的「数据清单」中连接
	- 因条件限制（存档资料不完整、无专业设备）不知道如何梳理？
	- 仓储工作中需要积累的内容应该分为三大类：一类是业务知识类（例：上架的流程、盘点的流程、WMS上架操作，偏向于业务操作），一类是管理类（例：标准化管理、6S管理、KPI管理），一类是实用工具类（例：Excel、PPT、写邮件）。业务知识类通用性较差，每个公司的业务流程都有所不同，但都差不多，属于死记硬背，熟能生巧的知识，在“防弹笔记法”中可以归为数据类。管理类个性化较强，每个人经历不同，解决的问题的差异，都会导致管理的差异，强调经验积累，在“防弹笔记法”中可以归为核心任务笔记。实用工具类通用性较强，可以在脱离公司背景下学习，细微差别在于可能每个公司的使用需求不同，使用场景不同，在“防弹笔记法”中可以归为数据类