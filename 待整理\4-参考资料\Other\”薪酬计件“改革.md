## 流程

- 梳理现场作业流程
- 统计各情景下的相关数据
- 单价核算
- 试运行监控
## Question

- 现场作业场景复杂，很难使用同一标准建模核算单价？同一场景甚至存在多种特殊作业场景？例如：复核环节因为订单解构不同，可分成提总订单复核、散单复核，且散单中票件比也会略有不同，需要逐步梳理
## 经验

- 环节求同存异
	- 注意区分每个环节工作步骤，明晰环节边界，有助于后续工作量取数和环节人效计算
	- 将同类或相似环节仓库进行归类，有助于后续工作阶段性推进
- 统计环节半年内工作量数据、工时数据计算环节人效
	- 环节
		- 收货：丰景台系统收货数据报表、
		- 上架：丰景台系统上架数据报表、
		- 拣货：丰景台系统拣货数据报表、
		- 播种：丰景台系统拣货数据报表（筛选播种订单）
			- 播种数据，系统无法导出，约等于拣货数据（存在多拣、少拣、部分订单不需要播种而且部分仓库采用手动播种）
		- 库存整理：工作量无法统计
		- 盘点：工作量无法统计
			- 很多仓库使用纸质盘点，线上进行的很少
		- 订单：无法统计数据，订单工作特殊，统计的数据没有意义
		- 综合：工作量无法统计
		- 发运：丰景台系统出库明细报表
			- 出库包裹数
	- 工时数据
		- 因不清楚具体员的岗位，很难直接从系统导出使用
	- 以环节方式逐一统计，类比的人效结果更加准确
- 统计半年内的个人工作量、工时数据
	- 根据之前沟通的环节，填写仓库所有二线员工的工作量、工时数据（包含临时工），临时工的数据可以当做一个整体或一个人
	- 工时量无法统计的数据暂时留空
	- 涉及统计到个人维度数据，需要明确具体工号、姓名
- 审核各环节目标人员投入
	- 超出环节目标人员投入，需要调整对应人员岗位
	- 匹配仓库近半年内个人薪资，计算环节总成本，对应环节工作量计算环节单价
	- 部分岗位无法计算工作量，综合、订单、库控全部按照“综合支持岗归类，且这部分人员信息取环节内人员平均薪资
	- 部分人员不隶属任何岗位，例如支援岗……，需要仓库统计支援到其它岗位的工时，以及对应工作量；对于部分环节工作量只能计算到环节，无法计算到个人头上，依据实际达成的环节效率，用工时折算工作量（例如某环节全部使用一个工号登录系统作业，临时工使用自有员工工号作业等）
- 单价核算
	- 匹配环节人员每月薪资，计算该环节月维度的平均人工成本，依据工作量计算环节单价
	- 考虑相同类型环节，不同仓库存在特殊业务场景（考虑对环节的额外投入以及人效目标影响，进而影响目标人员投入）的情况，规定各环节依据单价计算的总成本不得超改革前的总成本
- 试运行监控
	- 试运行期间，若计件薪资低于改革前薪资，取较高者发放员工薪资
	- 设定调节系数，主要是仓库业务场景多变，经常会因为大促、活动、增值服务等因素，导致环节作业难度提升，效率下降，进而影响员工薪资水平