```excel
	Sub text()
	Worksheets.Add.Name = "快学Excel"
	Snd End
```
- 对象的属性和方法的前后顺序是否可以调换？
- 如果需要对一个对象调用多个方法应该如何编写代码？调用多个属性应该如何编写代码？

```excel
	sub text()
	'第一种方法'
	rang("a1").copy
	rang("b1").PasteSpecial xlPastevalues
	
	'第二种方法'
	rang("a1").copy:rang("b1").PasteSpecial xlPastevalues
	end sub
```
- `xlPasteValues` 是 `.PasteSpecial` 方法的一个参数，为什么可以直接写在后面，而不是想一般的变成语言一样放在括号里面？
- `:`具体代表什么意思？

```excel
	Sub test3() 
	Dim ss As Range 
	For Each ss In Range(Sheet1.[b2],Cells (Rows.Count,2).End(xlUp)) 
		If ss.Value="男" Then 
			Worksheets.Add(after:=Sheets(Sheets.Count)).Name = ss.Offset (0,-1) '为什么不能用ss.Offset (0,-1).Name'
		End If 
	Next ss 
	End Sub
```
- 为什么不能用`ss.Offset (0,-1).Name`?

```excel
	sub test()
	MsgBox Range（"c:c").Find("李山"，Range（"c3")，xlFormulas) 'Find是单元格的方法，参数被小括号包裹'
	EndSub
	
	Sub 排序()
	Dim ss As Range
	Set ss =Range（"j1").CurrentRegion 'ss变量代表一个对象，前面要用set'
	ss.Sort Range("j1")，1，Range("m1")，，2,Header:=xlYes 'Sort是单元格是方法，参数没有被小括号包裹；2前面仅有一个,代表忽略参数'
	EndSiRange（ji)=“班级
```
- 使用对象方法时，参数应该如何写？

```excel
	Sub test()
	    arr = Sheet1.Range("a1:f1").Value '方法1'
	    arr1 = Sheet1.Range("a1:a8")'方法2'
	    arr1 = Application.WorksheetFunction.Transpose(arr1)'转置'
	    arr1 = Application.WorksheetFunction.Transpose(Application.WorksheetFunction.Transpose(arr1))'将从Excel范围（一行）获取的二维数组转化为一维数组'
	End Sub
```
- 从 Excel 范围获取值时，VBA 会默认将其存储为二维数组，即使它只有一行或一列
- 方法1与方法2都可以获取单元格的数组
- 从 Excel 2010 开始，64 位版本的 Excel 不再支持 `Application.Transpose()` 方法，但可以调用工作表函数`Application.WorksheetFunction.Transpose()`

```excel
	Filter()
	Application.WorksheetFunction.Filter()
```
- Filter() 函数
	- Filter() 是一个VBA函数，用于从数组或范围中返回满足指定条件的元素。
	- 它可以直接在VBA代码中使用，不需要引用WorksheetFunction。
	- Filter() 函数通常用于处理一维或二维数组，而不是整个工作表范围。
- Application.WorksheetFunction.Filter()
	- Application.WorksheetFunction.Filter() 是Excel工作表函数 FILTER 的VBA等价物。
	- 它需要通过 Application.WorksheetFunction 对象来调用。
	- 这个函数可以处理更复杂的数据集，包括多列和多行，并且可以应用多个过滤条件。
	- FILTER 函数是Excel 365和Excel 2019中的新功能，因此在使用 Application.WorksheetFunction.Filter() 之前，需要确保你的Excel版本支持 FILTER 函数。