它是一个基于 JavaScript 的图表和图表工具，可呈现受 Markdown 启发的文本定义，以动态创建和修改图表。

流程图由节点（几何形状）和边（箭头或线条）组成。Mermaid 代码定义了节点和边的制作方式，并适应不同的箭头类型、多向箭头以及与子图之间的任何链接。

**基本语法**

```
	```mermaid
		flowchart LR 
		id
	```
```
- id 是框中显示的内容
- flowchart也可以使用graph
- LR是流程图的方向（只能为大写字母），可能得流程图方向包括
	- TD - 自上而下
	- BT - 从下到上
	- RL - 从右到左
	- LR - 从左到右

**节点形状**

`[This is the text in the box]`
```mermaid
	flowchart LR
	id[This is the text in the box]
```

`(This is the text in the box)`
```mermaid
	flowchart LR
    id1(This is the text in the box)

```

`([This is the text in the box])`
```mermaid
	flowchart LR
    id1([This is the text in the box])
```

`[[This is the text in the box]]`
```mermaid
	flowchart LR
    id1[[This is the text in the box]]
```

`[(Database)]`
```mermaid
	flowchart LR
    id1[(Database)]
```

`{This is the text in the box}`
```mermaid
	flowchart LR
    id1{This is the text in the box}
```
`((This is the text in the circle))`
```mermaid
flowchart LR
    id1((This is the text in the circle))
```
**带箭头的链接**

```mermaid
	flowchart LR
    A-->B
    C--文本-->D
    E-.->F
    G-. text .-> H
```

**分组**

```mermaid
	flowchart LR
	subgraph "One"
		direction LR //使用 direction 语句来设置子图将呈现的方向
		A-->B
		A-->C
	end
	subgraph "Two"
		D-->E
		D-->F
	end
	One-->Two
```
- direction LR 使用 direction 语句来设置子图将呈现的方向
- One-->Two 表示子图的流程关系
- 
