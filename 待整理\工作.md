- 质量周报优化
	- 与战区关键指标考核对齐，动态刷新反应真实数据达成；
	- 包含内部、外包关键指标达成；
	- 展示仓库仓库质量问题发现、解决效果；
- 质量接口人考核方案优化
	- 从质量接口人最基本的工作要求层面细化考核方案；
	- 理论知识、指标监控、培训落地、现场整改、质量问题跟进5大维度细化；
	- 依据“夯实底盘、鼓励整改”逻辑进行考核
- 仓库主管、负责人考核优化
	- 结合仓库主管岗位职责细化考核方案
	- 关键指标达成、运营风险规避、主导现场整改三大维度细化
	- 依据“改善落地、指标达成”逻辑考核
- 问题件管理机制
	- 将问题件及时细化至仓库维度
	- 监督仓库完成问题件申诉、反馈工作
		- 问题件申诉规则、逻辑
		- 仓库问题件申诉、反馈完成情况监控
	- 完成仓库反馈问题件审核工作
- 遗失类问题件，在现金付款赔付收方后，如何与仓库进行费用划转。
- QMP问题件细化原则（战区）
	- 时间要求：每日上班、下班花费1小时时间用于系统问题件细化、反馈
	- 责任点部代码不能为空
	- 细化备注不能为空
- 遗失类问题件总是出现核查超过时效，但是在后期又找到的情况出现，此时理赔已赔付收方客户
- 区部反馈申诉问题件（在规定时效内，满足次数要求），总部未审核，责任界定错误，如何处理
- 遗失类问题件，在超过规定时效后找回，商品退还客户，是否可以与客户沟通免赔商品金额，月结抵扣
- 客户审核发现的问题点，如何闭环管理仓库进行改善。
- 每日客诉跟进，未能做到有效闭环，没有跟踪问题解决的实际情况并跟踪介入
- 每日晨会监控客诉，对于历史已经发生问题不知道是否需要上班
- 仓库私下与收方或者受赔方进行理赔处理，但引起客户重复界定赔付
- 对于仓库出现的问题件，仓库未能及时反馈异常，特别是盟仓和集运仓
- 电商仓库产品类型、时效要求及线路规划。
- 每周质量运营例会已开始流于形式，需要思考例会的目的和作用。
- 目前质量管理工作，日报已经流于形式，没有起到它应该有的作用（仓库能够养成习惯每日回顾仓库异常，历及时发现问题，处理问题），日报制度体系优化迫在眉睫。
- 质量周报的目的在于区域或者仓库能及时回顾了解上周关键指标情况，及时发现问题及时改善。一般处理数据需要花费2小时
- 日常质量管理，仓配负责人不关注区域客诉情况，月底赛马机制下发后，需要明细数据核对。日常如何处理方式更好
- 7月接手了星级员工认证的工作，当初不知道是什么原因就同意了。明明手上的工作已经很多了，现在回想起来，唯一记得的理由是这个是质量发起的工作
- 从我内心的认知来讲，工作中人员的编制、工作超负荷、增加新员工等问题都是应该是领导来考虑的问题，我的任务是告诉领导问题的严重性，证明需要加人员，同时把本职工作做好。但是近1个月的状态来说，人员的问题让我一度陷入被动，精疲力尽
- 质量规范标准不清楚，处理质量问题毫无经验
- 对于不经过大脑的请教，还是很耐心的回答，等于是对别人和自己的不负责任
- 仓储中心库内质量人员日常工作3大方向：
	- 质量培训宣导：质量相关知识的学习和宣导
		- 制度、标准的学习（仓储知识园地）
		- 作业流程标准化；
		- 仓库可视化VI导入
		- KPI指标监控、优化等；
		- 制度、标准的培训
		- 对新员工进行质量知识的相关培训；
		- 对在职员工进行质量相关工作知识点进行强化、培训宣导；
	- 质量监督反馈：库内质量的监督和跟进落地实施：
		- 每日库内质量巡检表
		- 每周库存准确率抽查（RF抽查）
	- 质量整改提升：库内质量问题的反馈、跟进和优化：
		- 发现库内质量问题及隐患；
		- 反馈库内质量问题并跟进督促整改；
		- 协助仓库经理主管进行仓储质量提升
- 质量体系的搭建除了内容的拔高（质量标准化，质量管理，质量辅导），人员引入，岗位配置体系（不同的仓库等级配置不同级别的人员）
- 每次双周工作回顾没有数据体现，需要做好日常积累沉淀
- 现阶段质量工作提炼不足，价值贡献部分没有准确的呈现出来，向上管理弱，导致没有真实体现工作意义
- 战区质量管理体系的搭建（包含接口人的培训年度计划、接口人考核制度更新）
- 关于仓库作业标准SOP、管理制度的体系搭建、奖罚制度更新；
- 战区仓库管理方法革新
- 战区飞行检查报告输出
- 仓库运作质量检查仓库自查工作统筹
- 仓库运营异常战区通报及结果应用
- 每日工作时间1/3时间或者2/3的时间处于会议阶段，无法集中精力处理其他事情
- 日常工作有大部分时间在沟通仓库的异常问题，或者和内部其他的部门在沟通临时交代下来的任务。6月8日，沟通合肥恒安仓理赔事件，合肥经开仓代收货款问题，临江美团库存问题，合肥经开仓客诉剔除问题
- 代收货款清单上传异常，导致货款无法回收。仓库端兜底方案，逐票核查清单，考虑纳入日常工作。
- 每周仓库PK数据重复性工作较大，需要设定模板，以提升工作效率
- 关于仓库出现的异常，除了目前在坚持做的异常分析和改善措施，对于结果应用没有形成固定的流程或者机制，且应用断断续续。
- 质量介入仓库库位申请最终目的是规范仓库库位编码的规范，同时对仓库库位数量进行审核，避免仓库幽灵库位的无下限的增加。目前看来只进行了库位编码规范的介入
- 仓库的异常问题需要战区逐条跟进，并关注介入处理进度
- 每次晨会老板故意把声音调很大，会议进行中时，心中忐忑，总是觉得有些事情没有做好，老板可能会反驳……让我无法集中注意力
- 早、中、晚刚开始学习工作时，可以先专注30分钟去做与当前项目或任务相关的但不那么重要的事务，这样的行动一般较为容易，能更轻松的完成，可以让自己更快的进入状态