---
number headings: first-level 1, start-at 1,max 6,1.1, auto
---
- 有效论证：不可能出现前提皆为真而结论为假的情况
- 不可能
	- 现实上不可能
	- 物理上不可能
	- 逻辑上不可能（所有的矛盾都是逻辑上不可能）

## 0.1 日常语言的复杂性

- 歧义
	- 语法歧义：同一句子不同的语句结构解读方式，同一语句会有不同意义
	- 语意歧义：若出现在语句中的词语，有不同的解释，则同一语句会有不同的语意
	- 语用歧义：语句的语意会因为情景或使用者的语气而有不同的意思
- 含混性：指某个概念、术语或命题缺乏明确的边界或清晰的适用条件，导致无法通过明确的规则判定其真值（例子：你还是个小孩子。——从生物学角度可能不是，但是从心里上可能是）
- 开放性：在知识表示与语义网中，开放世界假设认为系统无法掌握全部信息，因此未明确陈述的事实不自动视为假，而是“未知”。（例如，若数据库未记录“某人是医生”，系统不会推断“某人不是医生”）
## 0.2 形式语言的必要性

- 内容
	- 符号
		- 逻辑符号：它解释是固定不变的
		- 非逻辑符号：它是一个必须要被解释的符号
	- 形式规则：建构合宜的句式

## 0.3 命题逻辑语言

- 符号
	- 语句（或命题）符号：P、Q、R……
	- 真值函映（或连接词）：`¬,∧，∨，→，↔`
	- 辅助符号：`(,)`
- 行构规则
	- 每个语句符号都是句子
	- 如果P是一個句式，那么¬P也是句式
	- 如果P和Q都是句子，那么P∧Q、P∨Q、P→Q、P↔Q也都是句式
	- 除了上述规则所建构的句式外，没有其他句式
	- 优先次序：¬ 优先级最高，∧ , ∨ 次之，→ , ↔ 优先级最低；相同优先级的联结词按从左到右顺序
